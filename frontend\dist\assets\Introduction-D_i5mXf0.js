import{r as d,j as e,a as v}from"./index-BVS2LIo4.js";const k=({showPremiumOverlay:w,onBackToCourse:c,labPage:t,sectionCardId:n})=>{var x,h,m,b;const[i,u]=d.useState(!1),[s,f]=d.useState({}),o=t._id,g=()=>{u(!i)};console.log(n);const j=`
        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes scaleIn {
          from { transform: scale(0.95); opacity: 0; }
          to { transform: scale(1); opacity: 1; }
        }
        
        .animate-fadeIn {
          animation: fadeIn 0.6s ease-out forwards;
        }
        
        .animate-scaleIn {
          animation: scaleIn 0.5s ease-out forwards;
        }
        
        .hover-scale {
          transition: transform 0.3s ease;
        }
        
        .hover-scale:hover {
          transform: scale(1.05);
        }
      `;return d.useEffect(()=>{n&&(async()=>{var l,p;try{const{data:r}=await v.get(`/introduction/${o}/${n}`,{withCredentials:!0});r.success&&f(r.data)}catch(r){const N=(p=(l=r==null?void 0:r.response)==null?void 0:l.data)==null?void 0:p.message;console.error(N)}})()},[n,o]),console.log(s),e.jsxs("div",{className:"py-8",children:[e.jsx("style",{dangerouslySetInnerHTML:{__html:j}}),e.jsxs("div",{className:"max-w-6xl mx-auto px-4 md:px-6",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("button",{onClick:()=>{c&&c()},className:"inline-flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white hover:bg-white/20 transition-all duration-300 transform hover:scale-105",children:[e.jsx("span",{className:"text-xl",children:"←"}),e.jsx("span",{children:"Back to Course"})]})}),e.jsxs("div",{className:"text-center mb-16 animate-fadeIn",children:[e.jsxs("div",{className:"inline-block px-6 py-3 bg-gradient-to-r from-green-600 to-blue-600 text-white rounded-full text-sm font-medium mb-6 shadow-lg",children:["🤖 ",t==null?void 0:t.name]}),e.jsx("h2",{className:"text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight",children:e.jsx("span",{className:"bg-gradient-to-r from-green-400 to-blue-400 bg-clip-text text-transparent",children:t==null?void 0:t.name})}),e.jsx("p",{className:"text-xl text-white/80 max-w-3xl mx-auto leading-relaxed",children:t==null?void 0:t.description})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16",children:[e.jsxs("div",{className:"space-y-8 animate-fadeIn",children:[e.jsxs("div",{className:"bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl",children:[e.jsxs("h3",{className:"text-2xl font-bold text-white mb-4 flex items-center",children:[e.jsx("span",{className:"mr-3 text-3xl",children:"🎯"}),"Course Overview"]}),e.jsx("p",{className:"text-white/80 leading-relaxed",children:s==null?void 0:s.courseOverview})]}),e.jsxs("div",{className:"bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl",children:[e.jsxs("h3",{className:"text-2xl font-bold text-white mb-4 flex items-center",children:[e.jsx("span",{className:"mr-3 text-3xl",children:"💡"}),"Why Machine Learning?"]}),e.jsx("p",{className:"text-white/80 leading-relaxed",children:s==null?void 0:s.whyLearning})]}),e.jsxs("div",{className:"bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl",children:[e.jsxs("h3",{className:"text-2xl font-bold text-white mb-4 flex items-center",children:[e.jsx("span",{className:"mr-3 text-3xl",children:"🚀"}),"What ","You'll"," Learn"]}),e.jsx("p",{className:"text-white/80 leading-relaxed",children:s==null?void 0:s.whatYouWillLearn})]})]}),e.jsxs("div",{className:"space-y-8 animate-fadeIn",children:[e.jsxs("div",{className:"bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl",children:[e.jsxs("h3",{className:"text-2xl font-bold text-white mb-4 flex items-center",children:[e.jsx("span",{className:"mr-3 text-3xl",children:"📊"}),"Key Topics"]}),e.jsx("ul",{className:"space-y-3 text-white/80",children:(x=s==null?void 0:s.keyTopics)==null?void 0:x.map((a,l)=>e.jsxs("li",{className:"flex items-center",children:[e.jsx("span",{className:"w-2 h-2 bg-green-400 rounded-full mr-3"}),a]},l))})]}),e.jsxs("div",{className:"bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl",children:[e.jsxs("h3",{className:"text-2xl font-bold text-white mb-4 flex items-center",children:[e.jsx("span",{className:"mr-3 text-3xl",children:"🔧"}),"Tools & Technologies"]}),e.jsx("div",{className:"grid grid-cols-2 gap-4",children:(h=s==null?void 0:s.toolsAndTechnologies)==null?void 0:h.map((a,l)=>e.jsxs("div",{className:"text-center p-4 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10",children:[e.jsx("span",{className:"text-2xl mb-2 block",children:a.icon.secure_url}),e.jsx("span",{className:"text-white/80 text-sm",children:a.name})]},l))})]})]})]}),i&&e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16 animate-scaleIn",children:[e.jsxs("div",{className:"bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl",children:[e.jsxs("h3",{className:"text-2xl font-bold text-white mb-4 flex items-center",children:[e.jsx("span",{className:"mr-3 text-3xl",children:"🏆"}),"Advanced Concepts"]}),e.jsx("p",{className:"text-white/80 leading-relaxed mb-4",children:(m=s==null?void 0:s.conceptAndSkills)==null?void 0:m.description}),e.jsx("ul",{className:"space-y-2 text-white/80",children:(b=s==null?void 0:s.conceptAndSkills)==null?void 0:b.points.map((a,l)=>e.jsxs("li",{className:"flex items-center",children:[e.jsx("span",{className:"w-2 h-2 bg-blue-400 rounded-full mr-3"}),a]},l))})]}),e.jsxs("div",{className:"bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl",children:[e.jsxs("h3",{className:"text-2xl font-bold text-white mb-4 flex items-center",children:[e.jsx("span",{className:"mr-3 text-3xl",children:"💼"}),"Career Impact"]}),e.jsx("p",{className:"text-white/80 leading-relaxed",children:s==null?void 0:s.careerImpact})]})]}),e.jsx("div",{className:"text-center mb-12",children:e.jsx("button",{onClick:g,className:"bg-white/10 backdrop-blur-lg border border-white/20 text-white px-8 py-4 rounded-xl font-medium transition-all duration-300 transform hover:scale-105 hover:bg-white/20",children:i?"Show Less":"Read More"})}),e.jsx("div",{className:"bg-white/5 backdrop-blur-lg border border-white/10 rounded-2xl p-8 shadow-xl mb-12 animate-scaleIn",children:e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{className:"text-3xl font-bold text-white mb-4",children:"Ready to Start Your ML Journey?"}),e.jsx("p",{className:"text-white/80 text-lg mb-6 max-w-2xl mx-auto",children:"Join thousands of students who have successfully launched their machine learning careers with our comprehensive program."}),e.jsx("button",{onClick:w,className:"bg-gradient-to-r from-green-600 to-blue-600 text-white px-8 py-4 rounded-xl font-bold text-lg transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl hover-scale",children:"Get Premium Access →"})]})})]})]})};export{k as default};
