import { Link } from "react-router-dom";
import { motion } from "framer-motion";
import { useState } from "react";
import { useEffect } from "react";
import axiosInstance from "../../utils/axiosInstance";
import { PaymentModal } from "../ui";

// SVG patterns for course card headers
const CourseHeaderPatterns = {
  python: (
    <svg
      className="absolute inset-0 w-full h-full opacity-20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <pattern
          id="python-pattern"
          patternUnits="userSpaceOnUse"
          width="30"
          height="30"
          patternTransform="rotate(45)"
        >
          <rect width="6" height="6" fill="rgba(255,255,255,0.2)" />
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#python-pattern)" />
    </svg>
  ),
  ai: (
    <svg
      className="absolute inset-0 w-full h-full opacity-20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <pattern
          id="ai-pattern"
          patternUnits="userSpaceOnUse"
          width="40"
          height="40"
          patternTransform="rotate(30)"
        >
          <circle cx="20" cy="20" r="4" fill="rgba(255,255,255,0.2)" />
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#ai-pattern)" />
    </svg>
  ),
  fullstack: (
    <svg
      className="absolute inset-0 w-full h-full opacity-20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <pattern
          id="fullstack-pattern"
          patternUnits="userSpaceOnUse"
          width="50"
          height="50"
        >
          <path
            d="M0 25h50M25 0v50"
            stroke="rgba(255,255,255,0.2)"
            strokeWidth="2"
          />
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#fullstack-pattern)" />
    </svg>
  ),
  data: (
    <svg
      className="absolute inset-0 w-full h-full opacity-20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <pattern
          id="data-pattern"
          patternUnits="userSpaceOnUse"
          width="60"
          height="60"
        >
          <path
            d="M15 0L0 15M45 0L0 45M60 15L15 60M60 45L45 60"
            stroke="rgba(255,255,255,0.2)"
            strokeWidth="2"
          />
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#data-pattern)" />
    </svg>
  ),
  system: (
    <svg
      className="absolute inset-0 w-full h-full opacity-20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <pattern
          id="system-pattern"
          patternUnits="userSpaceOnUse"
          width="40"
          height="40"
        >
          <rect width="20" height="20" fill="rgba(255,255,255,0.1)" />
          <rect
            width="20"
            height="20"
            x="20"
            y="20"
            fill="rgba(255,255,255,0.1)"
          />
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#system-pattern)" />
    </svg>
  ),
  devops: (
    <svg
      className="absolute inset-0 w-full h-full opacity-20"
      xmlns="http://www.w3.org/2000/svg"
    >
      <defs>
        <pattern
          id="devops-pattern"
          patternUnits="userSpaceOnUse"
          width="60"
          height="60"
        >
          <path
            d="M30 0v60M0 30h60"
            stroke="rgba(255,255,255,0.15)"
            strokeWidth="3"
          />
          <circle
            cx="30"
            cy="30"
            r="10"
            fill="none"
            stroke="rgba(255,255,255,0.2)"
            strokeWidth="2"
          />
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#devops-pattern)" />
    </svg>
  ),
};

const styles = {
  textShadow: {
    textShadow: "0 2px 4px rgba(0, 0, 0, 0.3)",
  },
};

const Courses = () => {
  const [labs, setLabs] = useState([]);
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState(null);

  useEffect(() => {
    const fetchLabs = async () => {
      try {
        const response = await axiosInstance.get("/lab/all-labs");
        setLabs(response.data.labs);
      } catch (error) {
        const message = error?.response?.data?.message;
        console.error(message);
      }
    };

    fetchLabs();
  }, []);

  const handleStartLearning = (lab) => {
    setSelectedCourse(lab);
    setIsPaymentModalOpen(true);
  };

  const handleStaticCoursePayment = (courseName, courseType) => {
    const staticCourse = {
      name: courseName,
      labType: courseType,
      _id: courseType.toLowerCase()
    };
    setSelectedCourse(staticCourse);
    setIsPaymentModalOpen(true);
  };

  const closePaymentModal = () => {
    setIsPaymentModalOpen(false);
    setSelectedCourse(null);
  };

  const coursesBackgroundPattern = (
    <div className="absolute inset-0 overflow-hidden opacity-30">
      <svg className="w-full h-full" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <pattern
            id="courses-bg-pattern"
            patternUnits="userSpaceOnUse"
            width="100"
            height="100"
            patternTransform="rotate(45)"
          >
            <circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.2)" />
            <circle cx="25" cy="25" r="0.5" fill="rgba(255,255,255,0.2)" />
            <circle cx="75" cy="75" r="0.5" fill="rgba(255,255,255,0.2)" />
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#courses-bg-pattern)" />
      </svg>
    </div>
  );

  return (
    <div
      className="bg-transparent py-20 relative"
      style={{ minHeight: "400px" }}
    >
      {coursesBackgroundPattern}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[#131c2e]/30 to-[#131c2e]/50 z-0"></div>
      <div className="container mx-auto px-6 relative z-10">
        {/* Section Header */}
        <div className="text-center mb-16 relative">
          <div className="absolute inset-0 bg-gradient-radial from-blue-500/5 via-purple-500/5 to-transparent -z-10 blur-3xl"></div>

          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="inline-flex items-center px-4 py-2 bg-white/5 backdrop-blur-lg border border-white/10 rounded-full text-white text-sm font-medium mb-6 shadow-lg"
          >
            <span className="mr-2">🎓</span>
            Our Most Popular Courses
          </motion.div>

          <motion.h2
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.7, delay: 0.2 }}
            className="text-5xl md:text-6xl font-bold bg-gradient-to-r from-white via-blue-200 to-purple-200 bg-clip-text text-transparent mb-6"
            style={styles.textShadow}
          >
            Transform Your Career
          </motion.h2>

          <motion.p
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.7, delay: 0.4 }}
            className="text-lg text-blue-100/80 max-w-3xl italic mx-auto leading-relaxed"
          >
            Join thousands of professionals {"who've"} accelerated their careers
            with our industry-leading courses. Learn from experts, build real
            projects, and get job-ready skills.
          </motion.p>
        </div>

        {/* Course Grid */}

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 items-stretch">
          {labs?.map((lab, index) => {
            const delay = 0.1 + index * 0.1;
            const pattern =
              CourseHeaderPatterns[lab.labType?.toLowerCase()] ||
              CourseHeaderPatterns.default;

            const label = lab.labels?.[0] || "New";

            return (
              <motion.div
                key={lab._id}
                whileHover={{ y: -8, scale: 1.02 }}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay }}
                className="flex flex-col h-full bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-md rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-white/10"
              >
                <div className="relative bg-gradient-to-r from-blue-500/10 to-cyan-600/10 p-6 text-white overflow-hidden backdrop-blur-lg flex-grow">
                  {pattern}
                  <div className="flex items-center justify-between mb-4 relative z-10">
                    <div className="w-12 h-12 bg-white/5 backdrop-blur-lg rounded-xl flex items-center justify-center shadow-lg border border-white/10">
                      <img
                        src={lab.icon.secure_url}
                        alt={lab.name}
                        className="w-8 h-8 object-contain"
                      />
                    </div>
                    <span
                      className="bg-blue-500/20 backdrop-blur-lg text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg border border-white/10"
                      style={styles.textShadow}
                    >
                      {label}
                    </span>
                  </div>
                  <h3
                    className="text-2xl font-bold mb-2 relative z-10"
                    style={styles.textShadow}
                  >
                    {lab.name}
                  </h3>
                  <p
                    className="text-sm opacity-90 relative z-10"
                    style={styles.textShadow}
                  >
                    {lab.labType} Mastery
                  </p>
                </div>
                <div className="p-6">
                  <p className="text-gray-300 mb-4">{lab.description}</p>
                  <div className="mb-4">
                    <h4 className="font-semibold text-gray-200 mb-2">
                      What {"you'll"} learn:
                    </h4>
                    <div className="flex flex-wrap gap-2">
                      {lab.learningPoints?.map((point, idx) => (
                        <span
                          key={idx}
                          className="bg-blue-500/10 backdrop-blur-md text-blue-300 px-2 py-1 rounded text-sm border border-blue-500/20"
                        >
                          {point}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-sm text-gray-300 mb-4">
                    <span>⏱️ 12 weeks</span>
                    <span>👥 1,000+ learners</span>
                    <span>⭐ 4.8/5</span>
                  </div>
                  <button
                    onClick={() => handleStartLearning(lab)}
                    className="block w-full bg-gradient-to-r from-green-600/30 to-blue-600/30 hover:from-green-600/40 hover:to-blue-600/40 backdrop-blur-sm text-white py-3 px-4 rounded-lg font-medium text-center transition-all duration-300 border border-white/10 shadow-lg"
                    style={styles.textShadow}
                  >
                    Start Learning →
                  </button>
                </div>
              </motion.div>
            );
          })}

           Python Course
          <motion.div
            whileHover={{ y: -8, scale: 1.02 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-md rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-white/10"
          >
            <div className="relative bg-gradient-to-r from-green-500/10 to-blue-600/10 p-6 text-white overflow-hidden backdrop-blur-lg">
              {CourseHeaderPatterns.python}
              <div className="flex items-center justify-between mb-4 relative z-10">
                <div className="w-12 h-12 bg-white/5 backdrop-blur-lg rounded-xl flex items-center justify-center shadow-lg border border-white/10">
                  <img
                    src={courseIcons.python}
                    alt="Python"
                    className="w-8 h-8 object-contain"
                  />
                </div>
                <span
                  className="bg-green-500/20 backdrop-blur-lg text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg border border-white/10"
                  style={styles.textShadow}
                >
                  Most Popular
                </span>
              </div>
              <h3
                className="text-2xl font-bold mb-2 relative z-10"
                style={styles.textShadow}
              >
                Python Mastery
              </h3>
              <p
                className="text-sm opacity-90 relative z-10"
                style={styles.textShadow}
              >
                Complete Python Development
              </p>
            </div>
            <div className="p-6">
              <p className="text-gray-300 mb-4">
                Master Python from fundamentals to advanced concepts including
                web development, data analysis, and automation.
              </p>
              <div className="mb-4">
                <h4 className="font-semibold text-gray-200 mb-2">
                  What {"you'll"} learn:
                </h4>
                <div className="flex flex-wrap gap-2">
                  <span className="bg-blue-500/10 backdrop-blur-md text-blue-300 px-2 py-1 rounded text-sm border border-blue-500/20">
                    Web Development
                  </span>
                  <span className="bg-blue-500/10 backdrop-blur-md text-blue-300 px-2 py-1 rounded text-sm border border-blue-500/20">
                    Data Analysis
                  </span>
                  <span className="bg-blue-500/10 backdrop-blur-md text-blue-300 px-2 py-1 rounded text-sm border border-blue-500/20">
                    Automation
                  </span>
                  <span className="bg-blue-500/10 backdrop-blur-md text-blue-300 px-2 py-1 rounded text-sm border border-blue-500/20">
                    AI/ML Basics
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm text-gray-300 mb-4">
                <span>⏱️ 12 weeks</span>
                <span>👥 2,847 students</span>
                <span>⭐ 4.9/5</span>
              </div>
              <button
                onClick={() => handleStaticCoursePayment("Python Programming", "python")}
                className="block w-full bg-gradient-to-r from-green-600/30 to-blue-600/30 hover:from-green-600/40 hover:to-blue-600/40 backdrop-blur-sm text-white py-3 px-4 rounded-lg font-medium text-center transition-all duration-300 border border-white/10 shadow-lg"
                style={styles.textShadow}
              >
                Start Learning →
              </button>
            </div>
          </motion.div>

         
          <motion.div
            whileHover={{ y: -8, scale: 1.02 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-md rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-white/10"
          >
            <div className="relative bg-gradient-to-r from-purple-500/10 to-pink-600/10 p-6 text-white overflow-hidden backdrop-blur-lg">
              {CourseHeaderPatterns.ai}
              <div className="flex items-center justify-between mb-4 relative z-10">
                <div className="w-12 h-12 bg-white/5 backdrop-blur-lg rounded-xl flex items-center justify-center shadow-lg border border-white/10">
                  <img
                    src={courseIcons.ai}
                    alt="AI/ML"
                    className="w-8 h-8 object-contain"
                  />
                </div>
                <span
                  className="bg-red-500/20 backdrop-blur-lg text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg border border-white/10"
                  style={styles.textShadow}
                >
                  Hot
                </span>
              </div>
              <h3
                className="text-2xl font-bold mb-2 relative z-10"
                style={styles.textShadow}
              >
                AI & Machine Learning
              </h3>
              <p
                className="text-sm opacity-90 relative z-10"
                style={styles.textShadow}
              >
                Advanced AI Specialization
              </p>
            </div>
            <div className="p-6">
              <p className="text-gray-300 mb-4">
                Deep dive into machine learning algorithms, neural networks, and
                real-world AI applications with hands-on projects.
              </p>
              <div className="mb-4">
                <h4 className="font-semibold text-gray-200 mb-2">
                  What you'll learn:
                </h4>
                <div className="flex flex-wrap gap-2">
                  <span className="bg-purple-500/10 backdrop-blur-md text-purple-300 px-2 py-1 rounded text-sm border border-purple-500/20">
                    Deep Learning
                  </span>
                  <span className="bg-purple-500/10 backdrop-blur-md text-purple-300 px-2 py-1 rounded text-sm border border-purple-500/20">
                    Neural Networks
                  </span>
                  <span className="bg-purple-500/10 backdrop-blur-md text-purple-300 px-2 py-1 rounded text-sm border border-purple-500/20">
                    TensorFlow
                  </span>
                  <span className="bg-purple-500/10 backdrop-blur-md text-purple-300 px-2 py-1 rounded text-sm border border-purple-500/20">
                    Real Projects
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm text-gray-300 mb-4">
                <span>⏱️ 16 weeks</span>
                <span>👥 1,923 students</span>
                <span>⭐ 4.8/5</span>
              </div>
              <Link
                to="/ml-course"
                className="block w-full bg-gradient-to-r from-purple-600/30 to-pink-600/30 hover:from-purple-600/40 hover:to-pink-600/40 backdrop-blur-sm text-white py-3 px-4 rounded-lg font-medium text-center transition-all duration-300 border border-white/10 shadow-lg"
                style={styles.textShadow}
              >
                Start Learning →
              </Link>
            </div>
          </motion.div>

         
          <motion.div
            whileHover={{ y: -8, scale: 1.02 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-md rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-white/10"
          >
            <div className="relative bg-gradient-to-r from-blue-500/10 to-cyan-600/10 p-6 text-white overflow-hidden backdrop-blur-lg">
              {CourseHeaderPatterns.fullstack}
              <div className="flex items-center justify-between mb-4 relative z-10">
                <div className="w-12 h-12 bg-white/5 backdrop-blur-lg rounded-xl flex items-center justify-center shadow-lg border border-white/10">
                  <img
                    src={courseIcons.fullstack}
                    alt="Full Stack"
                    className="w-8 h-8 object-contain"
                  />
                </div>
                <span
                  className="bg-blue-500/20 backdrop-blur-lg text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg border border-white/10"
                  style={styles.textShadow}
                >
                  Career Ready
                </span>
              </div>
              <h3
                className="text-2xl font-bold mb-2 relative z-10"
                style={styles.textShadow}
              >
                Full Stack Developer
              </h3>
              <p
                className="text-sm opacity-90 relative z-10"
                style={styles.textShadow}
              >
                MERN Stack Mastery
              </p>
            </div>
            <div className="p-6">
              <p className="text-gray-300 mb-4">
                Build modern web applications with MongoDB, Express.js, React,
                and Node.js. Master the complete development workflow.
              </p>
              <div className="mb-4">
                <h4 className="font-semibold text-gray-200 mb-2">
                  What you'll learn:
                </h4>
                <div className="flex flex-wrap gap-2">
                  <span className="bg-cyan-500/10 backdrop-blur-md text-cyan-300 px-2 py-1 rounded text-sm border border-cyan-500/20">
                    React & Redux
                  </span>
                  <span className="bg-cyan-500/10 backdrop-blur-md text-cyan-300 px-2 py-1 rounded text-sm border border-cyan-500/20">
                    Node.js Backend
                  </span>
                  <span className="bg-cyan-500/10 backdrop-blur-md text-cyan-300 px-2 py-1 rounded text-sm border border-cyan-500/20">
                    Database Design
                  </span>
                  <span className="bg-cyan-500/10 backdrop-blur-md text-cyan-300 px-2 py-1 rounded text-sm border border-cyan-500/20">
                    API Development
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm text-gray-300 mb-4">
                <span>⏱️ 20 weeks</span>
                <span>👥 3,156 students</span>
                <span>⭐ 4.9/5</span>
              </div>
              <Link
                to="/fullstack-course"
                className="block w-full bg-gradient-to-r from-blue-600/30 to-cyan-600/30 hover:from-blue-600/40 hover:to-cyan-600/40 backdrop-blur-sm text-white py-3 px-4 rounded-lg font-medium text-center transition-all duration-300 border border-white/10 shadow-lg"
                style={styles.textShadow}
              >
                Start Learning →
              </Link>
            </div>
          </motion.div>

          
          <motion.div
            whileHover={{ y: -8, scale: 1.02 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-md rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-white/10"
          >
            <div className="relative bg-gradient-to-r from-orange-500/10 to-red-600/10 p-6 text-white overflow-hidden backdrop-blur-lg">
              {CourseHeaderPatterns.data}
              <div className="flex items-center justify-between mb-4 relative z-10">
                <div className="w-12 h-12 bg-white/5 backdrop-blur-lg rounded-xl flex items-center justify-center shadow-lg border border-white/10">
                  <img
                    src={courseIcons.data}
                    alt="Data Science"
                    className="w-8 h-8 object-contain"
                  />
                </div>
                <span
                  className="bg-orange-500/20 backdrop-blur-lg text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg border border-white/10"
                  style={styles.textShadow}
                >
                  New
                </span>
              </div>
              <h3
                className="text-2xl font-bold mb-2 relative z-10"
                style={styles.textShadow}
              >
                Data Science Complete
              </h3>
              <p
                className="text-sm opacity-90 relative z-10"
                style={styles.textShadow}
              >
                Analytics & Visualization
              </p>
            </div>
            <div className="p-6">
              <p className="text-gray-300 mb-4">
                Transform raw data into actionable insights using Python, R,
                SQL, and modern visualization tools.
              </p>
              <div className="mb-4">
                <h4 className="font-semibold text-gray-200 mb-2">
                  What you'll learn:
                </h4>
                <div className="flex flex-wrap gap-2">
                  <span className="bg-orange-500/10 backdrop-blur-md text-orange-300 px-2 py-1 rounded text-sm border border-orange-500/20">
                    Statistical Analysis
                  </span>
                  <span className="bg-orange-500/10 backdrop-blur-md text-orange-300 px-2 py-1 rounded text-sm border border-orange-500/20">
                    Data Visualization
                  </span>
                  <span className="bg-orange-500/10 backdrop-blur-md text-orange-300 px-2 py-1 rounded text-sm border border-orange-500/20">
                    Machine Learning
                  </span>
                  <span className="bg-orange-500/10 backdrop-blur-md text-orange-300 px-2 py-1 rounded text-sm border border-orange-500/20">
                    Big Data Tools
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm text-gray-300 mb-4">
                <span>⏱️ 18 weeks</span>
                <span>👥 2,234 students</span>
                <span>⭐ 4.7/5</span>
              </div>
              <Link
                to="/datascience-course"
                className="block w-full bg-gradient-to-r from-orange-600/30 to-red-600/30 hover:from-orange-600/40 hover:to-red-600/40 backdrop-blur-sm text-white py-3 px-4 rounded-lg font-medium text-center transition-all duration-300 border border-white/10 shadow-lg"
                style={styles.textShadow}
              >
                Start Learning →
              </Link>
            </div>
          </motion.div>

         
          <motion.div
            whileHover={{ y: -8, scale: 1.02 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
            className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-md rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-white/10"
          >
            <div className="relative bg-gradient-to-r from-teal-500/10 to-emerald-600/10 p-6 text-white overflow-hidden backdrop-blur-lg">
              {CourseHeaderPatterns.system}
              <div className="flex items-center justify-between mb-4 relative z-10">
                <div className="w-12 h-12 bg-white/5 backdrop-blur-lg rounded-xl flex items-center justify-center shadow-lg border border-white/10">
                  <img
                    src={courseIcons.system}
                    alt="System Design"
                    className="w-8 h-8 object-contain"
                  />
                </div>
                <span
                  className="bg-teal-500/20 backdrop-blur-lg text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg border border-white/10"
                  style={styles.textShadow}
                >
                  Advanced
                </span>
              </div>
              <h3
                className="text-2xl font-bold mb-2 relative z-10"
                style={styles.textShadow}
              >
                System Design
              </h3>
              <p
                className="text-sm opacity-90 relative z-10"
                style={styles.textShadow}
              >
                Architecture & Scalability
              </p>
            </div>
            <div className="p-6">
              <p className="text-gray-300 mb-4">
                Learn to design scalable systems and architectures that can
                handle millions of users with performance and reliability.
              </p>
              <div className="mb-4">
                <h4 className="font-semibold text-gray-200 mb-2">
                  What you'll learn:
                </h4>
                <div className="flex flex-wrap gap-2">
                  <span className="bg-teal-500/10 backdrop-blur-md text-teal-300 px-2 py-1 rounded text-sm border border-teal-500/20">
                    Distributed Systems
                  </span>
                  <span className="bg-teal-500/10 backdrop-blur-md text-teal-300 px-2 py-1 rounded text-sm border border-teal-500/20">
                    Microservices
                  </span>
                  <span className="bg-teal-500/10 backdrop-blur-md text-teal-300 px-2 py-1 rounded text-sm border border-teal-500/20">
                    Load Balancing
                  </span>
                  <span className="bg-teal-500/10 backdrop-blur-md text-teal-300 px-2 py-1 rounded text-sm border border-teal-500/20">
                    Database Scaling
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm text-gray-300 mb-4">
                <span>⏱️ 10 weeks</span>
                <span>👥 1,789 students</span>
                <span>⭐ 4.9/5</span>
              </div>
              <Link
                to="/system-design-course"
                className="block w-full bg-gradient-to-r from-teal-600/30 to-emerald-600/30 hover:from-teal-600/40 hover:to-emerald-600/40 backdrop-blur-sm text-white py-3 px-4 rounded-lg font-medium text-center transition-all duration-300 border border-white/10 shadow-lg"
                style={styles.textShadow}
              >
                Start Learning →
              </Link>
            </div>
          </motion.div>

         
          <motion.div
            whileHover={{ y: -8, scale: 1.02 }}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.6 }}
            className="bg-gradient-to-b from-[#1e293b]/10 to-transparent backdrop-blur-md rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 overflow-hidden border border-white/10"
          >
            <div className="relative bg-gradient-to-r from-blue-600/10 to-indigo-600/10 p-6 text-white overflow-hidden backdrop-blur-lg">
              {CourseHeaderPatterns.devops}
              <div className="flex items-center justify-between mb-4 relative z-10">
                <div className="w-12 h-12 bg-white/5 backdrop-blur-lg rounded-xl flex items-center justify-center shadow-lg border border-white/10">
                  <img
                    src={courseIcons.devops}
                    alt="DevOps"
                    className="w-8 h-8 object-contain"
                  />
                </div>
                <span
                  className="bg-indigo-500/20 backdrop-blur-lg text-white px-3 py-1 rounded-full text-xs font-semibold shadow-lg border border-white/10"
                  style={styles.textShadow}
                >
                  In Demand
                </span>
              </div>
              <h3
                className="text-2xl font-bold mb-2 relative z-10"
                style={styles.textShadow}
              >
                DevOps & Cloud
              </h3>
              <p
                className="text-sm opacity-90 relative z-10"
                style={styles.textShadow}
              >
                CI/CD & Infrastructure
              </p>
            </div>
            <div className="p-6">
              <p className="text-gray-300 mb-4">
                Master modern DevOps practices, CI/CD pipelines, infrastructure
                as code, and cloud platforms like AWS, Azure, and GCP.
              </p>
              <div className="mb-4">
                <h4 className="font-semibold text-gray-200 mb-2">
                  What you'll learn:
                </h4>
                <div className="flex flex-wrap gap-2">
                  <span className="bg-indigo-500/10 backdrop-blur-md text-indigo-300 px-2 py-1 rounded text-sm border border-indigo-500/20">
                    Docker & K8s
                  </span>
                  <span className="bg-indigo-500/10 backdrop-blur-md text-indigo-300 px-2 py-1 rounded text-sm border border-indigo-500/20">
                    CI/CD Pipelines
                  </span>
                  <span className="bg-indigo-500/10 backdrop-blur-md text-indigo-300 px-2 py-1 rounded text-sm border border-indigo-500/20">
                    Cloud Services
                  </span>
                  <span className="bg-indigo-500/10 backdrop-blur-md text-indigo-300 px-2 py-1 rounded text-sm border border-indigo-500/20">
                    Infrastructure
                  </span>
                </div>
              </div>
              <div className="flex items-center justify-between text-sm text-gray-300 mb-4">
                <span>⏱️ 14 weeks</span>
                <span>👥 2,143 students</span>
                <span>⭐ 4.8/5</span>
              </div>
              <Link
                to="/devops-course"
                className="block w-full bg-gradient-to-r from-blue-600/30 to-indigo-600/30 hover:from-blue-600/40 hover:to-indigo-600/40 backdrop-blur-sm text-white py-3 px-4 rounded-lg font-medium text-center transition-all duration-300 border border-white/10 shadow-lg"
                style={styles.textShadow}
              >
                Start Learning →
              </Link>
            </div>
          </motion.div> 
        </div>

        {/* View All Courses CTA */}
        <div className="text-center mt-16 relative">
          <div className="absolute inset-0 -z-10 bg-gradient-radial from-blue-500/10 via-transparent to-transparent blur-2xl"></div>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
          >
            <Link
              to="/courses"
              className="inline-flex items-center px-8 py-4 bg-white/5 hover:bg-white/10 border border-white/10 hover:border-white/20 backdrop-blur-xl rounded-full text-white text-lg font-medium transition-all duration-300 group shadow-lg hover:shadow-xl"
              style={styles.textShadow}
            >
              View All Courses
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M14 5l7 7m0 0l-7 7m7-7H3"
                />
              </svg>
            </Link>
          </motion.div>
        </div>
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={closePaymentModal}
        courseData={selectedCourse}
      />
    </div>
  );
};

export default Courses;
