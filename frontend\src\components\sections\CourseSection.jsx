import { motion } from "framer-motion";
import { <PERSON> } from "react-router-dom";
import { Courses } from "../features";
import { HotCourses, RecommendedCourses } from "../ui";
import { GradientOrbs } from "./common/BackgroundEffects";
import PaymentMethodSection from "./PaymentMethodSection";
import { useState } from "react";
import axios from "axios";
import { useEffect } from "react";
import axiosInstance from "../../utils/axiosInstance";
import { FaPython, FaReact, FaDatabase, FaBrain, FaRobot, FaCode, FaSearch, FaJs } from "react-icons/fa";

const CourseSection = ({ itemVariants }) => {

  const [hotLabs,setHotLabs] = useState([])
  const [advancedLabs,setAdvancedLabs] = useState([])

  // Course data for payment section
  const coursesData = [
    {
      id: "python",
      title: "Python Programming",
      description: "Master Python from basics to advanced concepts with hands-on projects",
      path: "/pythoncourse",
      icon: <FaPython className="text-4xl" />,
      level: "Beginner to Advanced",
      duration: "12 weeks",
      students: "15,000+",
      rating: 4.8,
      features: ["Interactive Labs", "Real Projects", "Live Sessions", "Certificate"],
      technologies: ["Python", "Django", "Flask", "Data Analysis"]
    },
    {
      id: "fullstack",
      title: "Full Stack Development",
      description: "Build complete web applications with modern technologies",
      path: "/fullstack-course",
      icon: <FaReact className="text-4xl" />,
      level: "Intermediate",
      duration: "16 weeks",
      students: "12,000+",
      rating: 4.9,
      features: ["MERN Stack", "Deployment", "API Development", "Portfolio"],
      technologies: ["React", "Node.js", "MongoDB", "Express"]
    },
    {
      id: "datascience",
      title: "Data Science",
      description: "Analyze data and build predictive models with Python",
      path: "/datascience-course",
      icon: <FaDatabase className="text-4xl" />,
      level: "Intermediate",
      duration: "20 weeks",
      students: "8,500+",
      rating: 4.7,
      features: ["Data Analysis", "Visualization", "ML Models", "Real Datasets"],
      technologies: ["Python", "Pandas", "NumPy", "Matplotlib"]
    },
    {
      id: "ml",
      title: "Machine Learning",
      description: "Build intelligent systems with machine learning algorithms",
      path: "/ml-course",
      icon: <FaBrain className="text-4xl" />,
      level: "Advanced",
      duration: "18 weeks",
      students: "6,200+",
      rating: 4.8,
      features: ["ML Algorithms", "Deep Learning", "Model Deployment", "Projects"],
      technologies: ["Python", "TensorFlow", "Scikit-learn", "Keras"]
    },
    {
      id: "ai",
      title: "Artificial Intelligence",
      description: "Explore AI concepts and build intelligent applications",
      path: "/ai-course",
      icon: <FaRobot className="text-4xl" />,
      level: "Advanced",
      duration: "22 weeks",
      students: "4,800+",
      rating: 4.9,
      features: ["Neural Networks", "NLP", "Computer Vision", "AI Ethics"],
      technologies: ["Python", "TensorFlow", "PyTorch", "OpenAI"]
    },
    {
      id: "dsa",
      title: "Data Structures & Algorithms",
      description: "Master DSA for technical interviews and competitive programming",
      path: "/data_strut",
      icon: <FaCode className="text-4xl" />,
      level: "Intermediate",
      duration: "14 weeks",
      students: "18,000+",
      rating: 4.8,
      features: ["Problem Solving", "Interview Prep", "Coding Practice", "Algorithms"],
      technologies: ["Python", "Java", "C++", "JavaScript"]
    },
    {
      id: "sql",
      title: "SQL Database",
      description: "Master database management and SQL queries",
      path: "/sql_50",
      icon: <FaSearch className="text-4xl" />,
      level: "Beginner to Intermediate",
      duration: "8 weeks",
      students: "10,500+",
      rating: 4.6,
      features: ["Query Optimization", "Database Design", "Joins", "Indexing"],
      technologies: ["MySQL", "PostgreSQL", "SQLite", "MongoDB"]
    },
    {
      id: "javascript",
      title: "JavaScript Mastery",
      description: "30 days of JavaScript challenges and projects",
      path: "/30_days_js",
      icon: <FaJs className="text-4xl" />,
      level: "Beginner to Advanced",
      duration: "30 days",
      students: "14,200+",
      rating: 4.7,
      features: ["Daily Challenges", "ES6+", "DOM Manipulation", "Async Programming"],
      technologies: ["JavaScript", "ES6+", "Node.js", "React"]
    }
  ];
  
    useEffect(() => {
    const fetchHotLabs = async () => {
      try {
        const { data } = await axiosInstance.get("/lab/hot-labs");
  
        if (data.success) {
          const labsWithExtras = data.labs.map((lab, index) => ({
            title: lab.name,
            image: lab.icon?.secure_url || "",
            rating: 4.5 + (index % 3) * 0.1,      
            students: `${1 + index}.k+`,        
          }));
  
          setHotLabs(labsWithExtras);
        }
      } catch (error) {
        const message = error?.response?.data?.message;
        console.error(message);
      }
    };

    const fetchAdvancedLabs = async () => {
      try {
        const { data } = await axiosInstance.get("/lab/advanced-labs");
  
        if (data.success) {
          const labsWithExtras = data.labs.map((lab) => ({
            title: lab.name,
            image: lab.icon?.secure_url || "",
            level: "Intermediate",      
            duration: `6 weeks`,        
          }));
  
          setAdvancedLabs(labsWithExtras)
        }
      } catch (error) {
        const message = error?.response?.data?.message;
        console.error(message);
      }
    };
  
    fetchHotLabs();
    fetchAdvancedLabs();
  }, []);

  console.log(hotLabs)
  
  
  return (
    <motion.div
      variants={itemVariants}
      className="py-16 px-6 relative overflow-hidden"
    >
      {/* Background elements */}
      <div className="absolute inset-0 overflow-hidden">
        <GradientOrbs />
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        <div className="courses-container text-white">
          <Courses />

          {/* Hot & Recommended Courses Grid */}
          <motion.section
            variants={itemVariants}
            className="container mx-auto px-4 py-12 max-w-3xl"
          >
            <div className="flex flex-col gap-8">
              <HotCourses
                hotLabs={hotLabs}
              />

              <RecommendedCourses
                advancedLabs={advancedLabs}
              />
            </div>
          </motion.section>
        </div>

        <div className="mt-12 text-center">
          <Link
            to="/courses"
            className="inline-block px-8 py-3 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg transition-colors duration-300 shadow-[0_0_15px_rgba(59,130,246,0.3)]"
          >
            View All Courses
          </Link>
        </div>

        {/* Payment Method Section */}
        <PaymentMethodSection
          coursesData={coursesData}
          itemVariants={itemVariants}
        />
      </div>
    </motion.div>
  );
};

export default CourseSection;
