import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaCreditCard, FaPaypal, FaGooglePay, FaApplePay, FaUniversity, FaBitcoin, FaCheck, FaLock } from 'react-icons/fa';
import { SiPhonepe, SiGooglepay, SiPaytm } from 'react-icons/si';

const PaymentMethodSection = ({ coursesData, itemVariants }) => {
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');
  const [selectedTier, setSelectedTier] = useState('premium');

  // Define color themes for each course
  const courseThemes = {
    python: {
      gradient: 'from-blue-500 to-blue-700',
      accent: 'blue-500',
      border: 'border-blue-500/30',
      bg: 'bg-blue-500/10',
      text: 'text-blue-300'
    },
    fullstack: {
      gradient: 'from-purple-500 to-indigo-600',
      accent: 'purple-500',
      border: 'border-purple-500/30',
      bg: 'bg-purple-500/10',
      text: 'text-purple-300'
    },
    datascience: {
      gradient: 'from-teal-500 to-cyan-600',
      accent: 'teal-500',
      border: 'border-teal-500/30',
      bg: 'bg-teal-500/10',
      text: 'text-teal-300'
    },
    ml: {
      gradient: 'from-green-500 to-emerald-600',
      accent: 'green-500',
      border: 'border-green-500/30',
      bg: 'bg-green-500/10',
      text: 'text-green-300'
    },
    ai: {
      gradient: 'from-violet-500 to-purple-600',
      accent: 'violet-500',
      border: 'border-violet-500/30',
      bg: 'bg-violet-500/10',
      text: 'text-violet-300'
    },
    dsa: {
      gradient: 'from-orange-500 to-red-600',
      accent: 'orange-500',
      border: 'border-orange-500/30',
      bg: 'bg-orange-500/10',
      text: 'text-orange-300'
    },
    sql: {
      gradient: 'from-cyan-500 to-blue-600',
      accent: 'cyan-500',
      border: 'border-cyan-500/30',
      bg: 'bg-cyan-500/10',
      text: 'text-cyan-300'
    },
    javascript: {
      gradient: 'from-yellow-500 to-orange-600',
      accent: 'yellow-500',
      border: 'border-yellow-500/30',
      bg: 'bg-yellow-500/10',
      text: 'text-yellow-300'
    }
  };

  const paymentMethods = [
    { id: 'card', name: 'Credit/Debit Card', icon: <FaCreditCard />, popular: true, fee: '2.9%', description: 'Visa, Mastercard, Amex' },
    { id: 'paypal', name: 'PayPal', icon: <FaPaypal />, popular: true, fee: '3.4%', description: 'Secure PayPal checkout' },
    { id: 'googlepay', name: 'Google Pay', icon: <SiGooglepay />, popular: false, fee: '0%', description: 'Quick mobile payment' },
    { id: 'phonepe', name: 'PhonePe', icon: <SiPhonepe />, popular: false, fee: '0%', description: 'UPI-based payment' },
    { id: 'paytm', name: 'Paytm', icon: <SiPaytm />, popular: false, fee: '1.5%', description: 'Wallet & UPI payment' },
    { id: 'upi', name: 'UPI', icon: <FaUniversity />, popular: true, fee: '0%', description: 'Direct bank transfer' },
    { id: 'applepay', name: 'Apple Pay', icon: <FaApplePay />, popular: false, fee: '0%', description: 'Touch ID & Face ID' },
    { id: 'crypto', name: 'Cryptocurrency', icon: <FaBitcoin />, popular: false, fee: '1%', description: 'Bitcoin, Ethereum' }
  ];

  // Pricing tiers for courses
  const pricingTiers = {
    python: { basic: 49, premium: 99, enterprise: 199 },
    fullstack: { basic: 79, premium: 149, enterprise: 299 },
    datascience: { basic: 69, premium: 129, enterprise: 249 },
    ml: { basic: 89, premium: 169, enterprise: 329 },
    ai: { basic: 99, premium: 189, enterprise: 379 },
    dsa: { basic: 39, premium: 79, enterprise: 159 },
    sql: { basic: 29, premium: 59, enterprise: 119 },
    javascript: { basic: 39, premium: 79, enterprise: 159 }
  };

  const getTheme = (courseId) => courseThemes[courseId] || courseThemes.python;

  const handleCourseSelect = (course) => {
    setSelectedCourse(course);
    setSelectedPaymentMethod('');
  };

  const handlePaymentSelect = (methodId) => {
    setSelectedPaymentMethod(methodId);
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5
      }
    }
  };

  return (
    <motion.div
      variants={itemVariants}
      className="py-16 px-6 relative overflow-hidden"
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <svg className="w-full h-full" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern
              id="payment-pattern"
              patternUnits="userSpaceOnUse"
              width="60"
              height="60"
              patternTransform="rotate(45)"
            >
              <circle cx="30" cy="30" r="2" fill="rgba(255,255,255,0.3)" />
              <circle cx="15" cy="15" r="1" fill="rgba(255,255,255,0.2)" />
              <circle cx="45" cy="45" r="1" fill="rgba(255,255,255,0.2)" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#payment-pattern)" />
        </svg>
      </div>

      <div className="max-w-7xl mx-auto relative z-10">
        {/* Section Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Choose Your Payment Method
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Select a course and your preferred payment method to get started with your learning journey
          </p>
        </motion.div>

        {/* Course Selection */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-12"
        >
          <h3 className="text-2xl font-bold text-white mb-6 text-center">Select a Course</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            {coursesData.map((course) => {
              const theme = getTheme(course.id);
              const isSelected = selectedCourse?.id === course.id;
              
              return (
                <motion.div
                  key={course.id}
                  variants={cardVariants}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => handleCourseSelect(course)}
                  className={`
                    relative cursor-pointer rounded-xl p-4 transition-all duration-300
                    ${isSelected 
                      ? `bg-gradient-to-r ${theme.gradient} border-2 border-white/30` 
                      : 'bg-transparent border-2 border-gray-600 hover:border-gray-500'
                    }
                  `}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`text-2xl ${isSelected ? 'text-white' : 'text-gray-300'}`}>
                      {course.icon}
                    </div>
                    <div>
                      <h4 className={`font-semibold ${isSelected ? 'text-white' : 'text-gray-200'}`}>
                        {course.title}
                      </h4>
                      <p className={`text-sm ${isSelected ? 'text-white/80' : 'text-gray-400'}`}>
                        {course.duration}
                      </p>
                    </div>
                  </div>
                  {isSelected && (
                    <div className="absolute top-2 right-2">
                      <FaCheck className="text-white text-sm" />
                    </div>
                  )}
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Pricing Tiers */}
        {selectedCourse && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-12"
          >
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-white mb-2">
                Choose Your Plan for {selectedCourse.title}
              </h3>
              <p className="text-gray-300">
                Select the plan that best fits your learning goals
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
              {['basic', 'premium', 'enterprise'].map((tier) => {
                const theme = getTheme(selectedCourse.id);
                const isSelected = selectedTier === tier;
                const price = pricingTiers[selectedCourse.id]?.[tier] || 99;

                return (
                  <motion.div
                    key={tier}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setSelectedTier(tier)}
                    className={`
                      relative cursor-pointer rounded-xl p-6 transition-all duration-300 border-2
                      ${isSelected
                        ? `bg-gradient-to-r ${theme.gradient} border-white/30`
                        : 'bg-transparent border-gray-600 hover:border-gray-500'
                      }
                    `}
                  >
                    {tier === 'premium' && (
                      <div className={`absolute -top-2 -right-2 px-3 py-1 bg-gradient-to-r ${theme.gradient} text-white text-xs rounded-full font-semibold`}>
                        Most Popular
                      </div>
                    )}

                    <div className="text-center">
                      <h4 className={`text-xl font-bold mb-2 capitalize ${isSelected ? 'text-white' : 'text-gray-200'}`}>
                        {tier}
                      </h4>
                      <div className={`text-3xl font-bold mb-4 ${isSelected ? 'text-white' : 'text-gray-200'}`}>
                        ${price}
                      </div>
                      <div className={`space-y-2 text-sm ${isSelected ? 'text-white/80' : 'text-gray-400'}`}>
                        {tier === 'basic' && (
                          <>
                            <div>✓ Course videos</div>
                            <div>✓ Basic exercises</div>
                            <div>✓ Community access</div>
                          </>
                        )}
                        {tier === 'premium' && (
                          <>
                            <div>✓ Everything in Basic</div>
                            <div>✓ Live sessions</div>
                            <div>✓ 1-on-1 mentoring</div>
                            <div>✓ Certificate</div>
                          </>
                        )}
                        {tier === 'enterprise' && (
                          <>
                            <div>✓ Everything in Premium</div>
                            <div>✓ Team collaboration</div>
                            <div>✓ Custom projects</div>
                            <div>✓ Priority support</div>
                          </>
                        )}
                      </div>
                    </div>

                    {isSelected && (
                      <div className="absolute top-2 right-2">
                        <FaCheck className="text-white text-sm" />
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        )}

        {/* Payment Methods */}
        {selectedCourse && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-12"
          >
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-white mb-2">
                Payment Methods
              </h3>
              <p className="text-gray-300">
                Choose your preferred payment method
              </p>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
              {paymentMethods.map((method) => {
                const theme = getTheme(selectedCourse.id);
                const isSelected = selectedPaymentMethod === method.id;
                
                return (
                  <motion.div
                    key={method.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => handlePaymentSelect(method.id)}
                    className={`
                      relative cursor-pointer rounded-xl p-6 transition-all duration-300 border-2
                      ${isSelected 
                        ? `bg-gradient-to-r ${theme.gradient} border-white/30` 
                        : 'bg-transparent border-gray-600 hover:border-gray-500'
                      }
                    `}
                  >
                    {method.popular && (
                      <div className={`absolute -top-2 -right-2 px-2 py-1 bg-gradient-to-r ${theme.gradient} text-white text-xs rounded-full font-semibold`}>
                        Popular
                      </div>
                    )}
                    
                    <div className="text-center">
                      <div className={`text-3xl mb-3 ${isSelected ? 'text-white' : 'text-gray-300'}`}>
                        {method.icon}
                      </div>
                      <h4 className={`font-semibold mb-1 ${isSelected ? 'text-white' : 'text-gray-200'}`}>
                        {method.name}
                      </h4>
                      <p className={`text-xs mb-2 ${isSelected ? 'text-white/80' : 'text-gray-400'}`}>
                        {method.description}
                      </p>
                      <div className={`text-sm font-medium ${isSelected ? 'text-white' : theme.text}`}>
                        Fee: {method.fee}
                      </div>
                    </div>
                    
                    {isSelected && (
                      <div className="absolute top-2 right-2">
                        <FaCheck className="text-white text-sm" />
                      </div>
                    )}
                  </motion.div>
                );
              })}
            </div>
          </motion.div>
        )}

        {/* Payment Summary */}
        {selectedCourse && selectedPaymentMethod && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-2xl mx-auto"
          >
            <div className={`bg-transparent border-2 ${getTheme(selectedCourse.id).border} rounded-xl p-8`}>
              <div className="flex items-center justify-center mb-6">
                <FaLock className={`text-2xl ${getTheme(selectedCourse.id).text} mr-2`} />
                <h3 className="text-2xl font-bold text-white">Secure Payment</h3>
              </div>
              
              <div className="space-y-4 mb-6">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Course:</span>
                  <span className="text-white font-semibold">{selectedCourse.title}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Plan:</span>
                  <span className="text-white capitalize">{selectedTier}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Duration:</span>
                  <span className="text-white">{selectedCourse.duration}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Payment Method:</span>
                  <span className="text-white">{paymentMethods.find(m => m.id === selectedPaymentMethod)?.name}</span>
                </div>
                <div className="border-t border-gray-600 pt-4 space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-300">Subtotal:</span>
                    <span className="text-white">${pricingTiers[selectedCourse.id]?.[selectedTier] || 99}.00</span>
                  </div>
                  {paymentMethods.find(m => m.id === selectedPaymentMethod)?.fee !== '0%' && (
                    <div className="flex justify-between items-center">
                      <span className="text-gray-300">Processing Fee:</span>
                      <span className="text-white">
                        {paymentMethods.find(m => m.id === selectedPaymentMethod)?.fee}
                      </span>
                    </div>
                  )}
                  <div className="flex justify-between items-center border-t border-gray-600 pt-2">
                    <span className="text-lg font-semibold text-gray-300">Total:</span>
                    <span className="text-2xl font-bold text-white">
                      ${pricingTiers[selectedCourse.id]?.[selectedTier] || 99}.00
                    </span>
                  </div>
                </div>
              </div>
              
              <button className={`w-full py-4 px-6 bg-gradient-to-r ${getTheme(selectedCourse.id).gradient} text-white font-semibold rounded-lg hover:opacity-90 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl`}>
                Proceed to Payment
              </button>

              <div className="mt-4 flex items-center justify-center space-x-4 text-sm text-gray-400">
                <div className="flex items-center space-x-1">
                  <FaLock className="text-green-400" />
                  <span>SSL Secured</span>
                </div>
                <div className="flex items-center space-x-1">
                  <FaCheck className="text-green-400" />
                  <span>256-bit Encryption</span>
                </div>
                <div className="flex items-center space-x-1">
                  <FaCheck className="text-green-400" />
                  <span>PCI Compliant</span>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </motion.div>
  );
};

export default PaymentMethodSection;
