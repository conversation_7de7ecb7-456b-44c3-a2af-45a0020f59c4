import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaCreditCard, FaPaypal, FaGooglePay, FaApplePay, FaUniversity, FaBitcoin, FaCheck, FaLock, FaStar, FaUsers, FaClock } from 'react-icons/fa';
import { SiPhonepe, SiGooglepay, SiPaytm } from 'react-icons/si';

const CoursePaymentSection = ({ courseId, courseName, showPremiumOverlay }) => {
  const [selectedTier, setSelectedTier] = useState('premium');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');

  // Course-specific themes
  const courseThemes = {
    pythoncourse: {
      gradient: 'from-blue-500 to-blue-700',
      accent: 'blue-500',
      border: 'border-blue-500/30',
      bg: 'bg-blue-500/10',
      text: 'text-blue-300'
    },
    'fullstack-course': {
      gradient: 'from-purple-500 to-indigo-600',
      accent: 'purple-500',
      border: 'border-purple-500/30',
      bg: 'bg-purple-500/10',
      text: 'text-purple-300'
    },
    'datascience-course': {
      gradient: 'from-teal-500 to-cyan-600',
      accent: 'teal-500',
      border: 'border-teal-500/30',
      bg: 'bg-teal-500/10',
      text: 'text-teal-300'
    },
    'ml-course': {
      gradient: 'from-green-500 to-emerald-600',
      accent: 'green-500',
      border: 'border-green-500/30',
      bg: 'bg-green-500/10',
      text: 'text-green-300'
    },
    'ai-course': {
      gradient: 'from-violet-500 to-purple-600',
      accent: 'violet-500',
      border: 'border-violet-500/30',
      bg: 'bg-violet-500/10',
      text: 'text-violet-300'
    },
    'dsa-course': {
      gradient: 'from-orange-500 to-red-600',
      accent: 'orange-500',
      border: 'border-orange-500/30',
      bg: 'bg-orange-500/10',
      text: 'text-orange-300'
    },
    'sql-course': {
      gradient: 'from-cyan-500 to-blue-600',
      accent: 'cyan-500',
      border: 'border-cyan-500/30',
      bg: 'bg-cyan-500/10',
      text: 'text-cyan-300'
    },
    javascript: {
      gradient: 'from-yellow-500 to-orange-600',
      accent: 'yellow-500',
      border: 'border-yellow-500/30',
      bg: 'bg-yellow-500/10',
      text: 'text-yellow-300'
    }
  };

  const paymentMethods = [
    { id: 'card', name: 'Credit/Debit Card', icon: <FaCreditCard />, popular: true, fee: '2.9%', description: 'Visa, Mastercard, Amex' },
    { id: 'paypal', name: 'PayPal', icon: <FaPaypal />, popular: true, fee: '3.4%', description: 'Secure PayPal checkout' },
    { id: 'upi', name: 'UPI', icon: <FaUniversity />, popular: true, fee: '0%', description: 'Direct bank transfer' },
    { id: 'googlepay', name: 'Google Pay', icon: <SiGooglepay />, popular: false, fee: '0%', description: 'Quick mobile payment' },
    { id: 'phonepe', name: 'PhonePe', icon: <SiPhonepe />, popular: false, fee: '0%', description: 'UPI-based payment' },
    { id: 'paytm', name: 'Paytm', icon: <SiPaytm />, popular: false, fee: '1.5%', description: 'Wallet & UPI payment' },
    { id: 'applepay', name: 'Apple Pay', icon: <FaApplePay />, popular: false, fee: '0%', description: 'Touch ID & Face ID' },
    { id: 'crypto', name: 'Cryptocurrency', icon: <FaBitcoin />, popular: false, fee: '1%', description: 'Bitcoin, Ethereum' }
  ];

  // Pricing tiers (in Indian Rupees)
  const pricingTiers = {
    basic: { price: 199, features: ['Course videos', 'Basic exercises', 'Community access', 'Mobile app access'] },
    premium: { price: 499, features: ['Everything in Basic', 'Live sessions', '1-on-1 mentoring', 'Certificate', 'Priority support'] },
    enterprise: { price: 999, features: ['Everything in Premium', 'Team collaboration', 'Custom projects', 'Dedicated support', 'Advanced analytics'] }
  };

  const getTheme = () => courseThemes[courseId] || courseThemes.pythoncourse;

  const handlePaymentSelect = (methodId) => {
    setSelectedPaymentMethod(methodId);
  };

  const theme = getTheme();

  return (
    <div className="min-h-screen bg-transparent py-12 px-6">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <svg className="w-full h-full" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern
              id="payment-bg-pattern"
              patternUnits="userSpaceOnUse"
              width="60"
              height="60"
              patternTransform="rotate(45)"
            >
              <circle cx="30" cy="30" r="2" fill="rgba(255,255,255,0.3)" />
              <circle cx="15" cy="15" r="1" fill="rgba(255,255,255,0.2)" />
              <circle cx="45" cy="45" r="1" fill="rgba(255,255,255,0.2)" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#payment-bg-pattern)" />
        </svg>
      </div>

      <div className="max-w-6xl mx-auto relative z-10">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Enroll in {courseName}
          </h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Choose your plan and payment method to start your learning journey
          </p>
        </motion.div>

        {/* Course Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12"
        >
          <div className={`bg-transparent border-2 ${theme.border} rounded-xl p-6 text-center`}>
            <FaStar className={`text-3xl ${theme.text} mx-auto mb-2`} />
            <div className="text-2xl font-bold text-white">4.8/5</div>
            <div className="text-gray-300">Student Rating</div>
          </div>
          <div className={`bg-transparent border-2 ${theme.border} rounded-xl p-6 text-center`}>
            <FaUsers className={`text-3xl ${theme.text} mx-auto mb-2`} />
            <div className="text-2xl font-bold text-white">15,000+</div>
            <div className="text-gray-300">Students Enrolled</div>
          </div>
          <div className={`bg-transparent border-2 ${theme.border} rounded-xl p-6 text-center`}>
            <FaClock className={`text-3xl ${theme.text} mx-auto mb-2`} />
            <div className="text-2xl font-bold text-white">12 weeks</div>
            <div className="text-gray-300">Course Duration</div>
          </div>
        </motion.div>

        {/* Pricing Tiers */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          className="mb-12"
        >
          <h3 className="text-3xl font-bold text-white text-center mb-8">Choose Your Plan</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {Object.entries(pricingTiers).map(([tier, details]) => {
              const isSelected = selectedTier === tier;
              const isPopular = tier === 'premium';
              
              return (
                <motion.div
                  key={tier}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => setSelectedTier(tier)}
                  className={`
                    relative cursor-pointer rounded-xl p-8 transition-all duration-300 border-2
                    ${isSelected 
                      ? `bg-gradient-to-r ${theme.gradient} border-white/30` 
                      : 'bg-transparent border-gray-600 hover:border-gray-500'
                    }
                  `}
                >
                  {isPopular && (
                    <div className={`absolute -top-3 left-1/2 transform -translate-x-1/2 px-4 py-1 bg-gradient-to-r ${theme.gradient} text-white text-sm rounded-full font-semibold`}>
                      Most Popular
                    </div>
                  )}
                  
                  <div className="text-center">
                    <h4 className={`text-2xl font-bold mb-2 capitalize ${isSelected ? 'text-white' : 'text-gray-200'}`}>
                      {tier}
                    </h4>
                    <div className={`text-4xl font-bold mb-6 ${isSelected ? 'text-white' : 'text-gray-200'}`}>
                      ₹{details.price.toLocaleString('en-IN')}
                    </div>
                    <div className={`space-y-3 text-left ${isSelected ? 'text-white/90' : 'text-gray-400'}`}>
                      {details.features.map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <FaCheck className={`text-sm ${isSelected ? 'text-white' : theme.text}`} />
                          <span>{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  {isSelected && (
                    <div className="absolute top-4 right-4">
                      <FaCheck className="text-white text-lg" />
                    </div>
                  )}
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Payment Methods */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="mb-12"
        >
          <h3 className="text-3xl font-bold text-white text-center mb-8">Payment Methods</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {paymentMethods.map((method) => {
              const isSelected = selectedPaymentMethod === method.id;
              
              return (
                <motion.div
                  key={method.id}
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={() => handlePaymentSelect(method.id)}
                  className={`
                    relative cursor-pointer rounded-xl p-4 transition-all duration-300 border-2
                    ${isSelected 
                      ? `bg-gradient-to-r ${theme.gradient} border-white/30` 
                      : 'bg-transparent border-gray-600 hover:border-gray-500'
                    }
                  `}
                >
                  {method.popular && (
                    <div className={`absolute -top-2 -right-2 px-2 py-1 bg-gradient-to-r ${theme.gradient} text-white text-xs rounded-full font-semibold`}>
                      Popular
                    </div>
                  )}
                  
                  <div className="text-center">
                    <div className={`text-2xl mb-2 ${isSelected ? 'text-white' : 'text-gray-300'}`}>
                      {method.icon}
                    </div>
                    <h4 className={`font-semibold text-sm mb-1 ${isSelected ? 'text-white' : 'text-gray-200'}`}>
                      {method.name}
                    </h4>
                    <p className={`text-xs ${isSelected ? 'text-white/80' : 'text-gray-400'}`}>
                      Fee: {method.fee}
                    </p>
                  </div>
                  
                  {isSelected && (
                    <div className="absolute top-2 right-2">
                      <FaCheck className="text-white text-sm" />
                    </div>
                  )}
                </motion.div>
              );
            })}
          </div>
        </motion.div>

        {/* Payment Summary */}
        {selectedPaymentMethod && (
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="max-w-2xl mx-auto"
          >
            <div className={`bg-transparent border-2 ${theme.border} rounded-xl p-8`}>
              <div className="flex items-center justify-center mb-6">
                <FaLock className={`text-2xl ${theme.text} mr-2`} />
                <h3 className="text-2xl font-bold text-white">Secure Checkout</h3>
              </div>
              
              <div className="space-y-4 mb-6">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Course:</span>
                  <span className="text-white font-semibold">{courseName}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Plan:</span>
                  <span className="text-white capitalize">{selectedTier}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Payment Method:</span>
                  <span className="text-white">{paymentMethods.find(m => m.id === selectedPaymentMethod)?.name}</span>
                </div>
                <div className="border-t border-gray-600 pt-4">
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-semibold text-gray-300">Total:</span>
                    <span className="text-3xl font-bold text-white">
                      ₹{pricingTiers[selectedTier].price.toLocaleString('en-IN')}
                    </span>
                  </div>
                </div>
              </div>
              
              <button 
                onClick={showPremiumOverlay}
                className={`w-full py-4 px-6 bg-gradient-to-r ${theme.gradient} text-white font-semibold rounded-lg hover:opacity-90 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl`}
              >
                Complete Payment
              </button>
              
              <div className="mt-4 flex items-center justify-center space-x-4 text-sm text-gray-400">
                <div className="flex items-center space-x-1">
                  <FaLock className="text-green-400" />
                  <span>SSL Secured</span>
                </div>
                <div className="flex items-center space-x-1">
                  <FaCheck className="text-green-400" />
                  <span>256-bit Encryption</span>
                </div>
                <div className="flex items-center space-x-1">
                  <FaCheck className="text-green-400" />
                  <span>PCI Compliant</span>
                </div>
              </div>
            </div>
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default CoursePaymentSection;
