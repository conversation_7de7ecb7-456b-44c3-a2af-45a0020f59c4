import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { FaCreditCard, FaPaypal, FaGooglePay, FaApplePay, FaUniversity, FaBitcoin, FaCheck, FaLock, FaStar, FaUsers, FaClock, FaTimes } from 'react-icons/fa';
import { SiPhonepe, SiGooglepay, SiPaytm } from 'react-icons/si';

const PaymentModal = ({ isOpen, onClose, courseData }) => {
  const [selectedTier, setSelectedTier] = useState('premium');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('');

  if (!courseData) return null;

  // Course-specific themes
  const courseThemes = {
    python: {
      gradient: 'from-blue-500 to-blue-700',
      accent: 'blue-500',
      border: 'border-blue-500/30',
      bg: 'bg-blue-500/10',
      text: 'text-blue-300'
    },
    fullstack: {
      gradient: 'from-purple-500 to-indigo-600',
      accent: 'purple-500',
      border: 'border-purple-500/30',
      bg: 'bg-purple-500/10',
      text: 'text-purple-300'
    },
    datascience: {
      gradient: 'from-teal-500 to-cyan-600',
      accent: 'teal-500',
      border: 'border-teal-500/30',
      bg: 'bg-teal-500/10',
      text: 'text-teal-300'
    },
    default: {
      gradient: 'from-blue-500 to-blue-700',
      accent: 'blue-500',
      border: 'border-blue-500/30',
      bg: 'bg-blue-500/10',
      text: 'text-blue-300'
    }
  };

  const paymentMethods = [
    { id: 'card', name: 'Credit/Debit Card', icon: <FaCreditCard />, popular: true, fee: '2.9%' },
    { id: 'paypal', name: 'PayPal', icon: <FaPaypal />, popular: true, fee: '3.4%' },
    { id: 'upi', name: 'UPI', icon: <FaUniversity />, popular: true, fee: '0%' },
    { id: 'googlepay', name: 'Google Pay', icon: <SiGooglepay />, popular: false, fee: '0%' },
    { id: 'phonepe', name: 'PhonePe', icon: <SiPhonepe />, popular: false, fee: '0%' },
    { id: 'paytm', name: 'Paytm', icon: <SiPaytm />, popular: false, fee: '1.5%' }
  ];

  // Pricing tiers (in Indian Rupees)
  const pricingTiers = {
    basic: { price: 199, features: ['Course videos', 'Basic exercises', 'Community access', 'Mobile app access'] },
    premium: { price: 499, features: ['Everything in Basic', 'Live sessions', '1-on-1 mentoring', 'Certificate', 'Priority support'] },
    enterprise: { price: 999, features: ['Everything in Premium', 'Team collaboration', 'Custom projects', 'Dedicated support', 'Advanced analytics'] }
  };

  const getTheme = () => {
    const courseType = courseData.labType?.toLowerCase() || courseData.name?.toLowerCase() || 'default';
    return courseThemes[courseType] || courseThemes.default;
  };

  const theme = getTheme();

  const modalVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 0.8 }
  };

  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 },
    exit: { opacity: 0 }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          variants={backdropVariants}
          initial="hidden"
          animate="visible"
          exit="exit"
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          <motion.div
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            transition={{ type: "spring", damping: 25, stiffness: 300 }}
            className="bg-gradient-to-br from-[#1a3c50] to-[#010509] rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto border border-white/10"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Header */}
            <div className="sticky top-0 bg-gradient-to-br from-[#1a3c50] to-[#010509] border-b border-white/10 p-6 flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-bold text-white">Enroll in {courseData.name}</h2>
                <p className="text-gray-300">Choose your plan and payment method</p>
              </div>
              <button
                onClick={onClose}
                className="text-gray-400 hover:text-white transition-colors p-2"
              >
                <FaTimes className="text-xl" />
              </button>
            </div>

            <div className="p-6">
              {/* Course Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                <div className={`bg-transparent border-2 ${theme.border} rounded-xl p-4 text-center`}>
                  <FaStar className={`text-2xl ${theme.text} mx-auto mb-2`} />
                  <div className="text-xl font-bold text-white">4.8/5</div>
                  <div className="text-gray-300 text-sm">Student Rating</div>
                </div>
                <div className={`bg-transparent border-2 ${theme.border} rounded-xl p-4 text-center`}>
                  <FaUsers className={`text-2xl ${theme.text} mx-auto mb-2`} />
                  <div className="text-xl font-bold text-white">15,000+</div>
                  <div className="text-gray-300 text-sm">Students Enrolled</div>
                </div>
                <div className={`bg-transparent border-2 ${theme.border} rounded-xl p-4 text-center`}>
                  <FaClock className={`text-2xl ${theme.text} mx-auto mb-2`} />
                  <div className="text-xl font-bold text-white">12 weeks</div>
                  <div className="text-gray-300 text-sm">Course Duration</div>
                </div>
              </div>

              {/* Pricing Tiers */}
              <div className="mb-8">
                <h3 className="text-2xl font-bold text-white text-center mb-6">Choose Your Plan</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {Object.entries(pricingTiers).map(([tier, details]) => {
                    const isSelected = selectedTier === tier;
                    const isPopular = tier === 'premium';
                    
                    return (
                      <motion.div
                        key={tier}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => setSelectedTier(tier)}
                        className={`
                          relative cursor-pointer rounded-xl p-6 transition-all duration-300 border-2
                          ${isSelected 
                            ? `bg-gradient-to-r ${theme.gradient} border-white/30` 
                            : 'bg-transparent border-gray-600 hover:border-gray-500'
                          }
                        `}
                      >
                        {isPopular && (
                          <div className={`absolute -top-3 left-1/2 transform -translate-x-1/2 px-3 py-1 bg-gradient-to-r ${theme.gradient} text-white text-xs rounded-full font-semibold`}>
                            Most Popular
                          </div>
                        )}
                        
                        <div className="text-center">
                          <h4 className={`text-xl font-bold mb-2 capitalize ${isSelected ? 'text-white' : 'text-gray-200'}`}>
                            {tier}
                          </h4>
                          <div className={`text-3xl font-bold mb-4 ${isSelected ? 'text-white' : 'text-gray-200'}`}>
                            ₹{details.price.toLocaleString('en-IN')}
                          </div>
                          <div className={`space-y-2 text-left text-sm ${isSelected ? 'text-white/90' : 'text-gray-400'}`}>
                            {details.features.map((feature, index) => (
                              <div key={index} className="flex items-center space-x-2">
                                <FaCheck className={`text-xs ${isSelected ? 'text-white' : theme.text}`} />
                                <span>{feature}</span>
                              </div>
                            ))}
                          </div>
                        </div>
                        
                        {isSelected && (
                          <div className="absolute top-3 right-3">
                            <FaCheck className="text-white text-sm" />
                          </div>
                        )}
                      </motion.div>
                    );
                  })}
                </div>
              </div>

              {/* Payment Methods */}
              <div className="mb-8">
                <h3 className="text-2xl font-bold text-white text-center mb-6">Payment Methods</h3>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {paymentMethods.map((method) => {
                    const isSelected = selectedPaymentMethod === method.id;
                    
                    return (
                      <motion.div
                        key={method.id}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                        onClick={() => setSelectedPaymentMethod(method.id)}
                        className={`
                          relative cursor-pointer rounded-xl p-4 transition-all duration-300 border-2
                          ${isSelected 
                            ? `bg-gradient-to-r ${theme.gradient} border-white/30` 
                            : 'bg-transparent border-gray-600 hover:border-gray-500'
                          }
                        `}
                      >
                        {method.popular && (
                          <div className={`absolute -top-2 -right-2 px-2 py-1 bg-gradient-to-r ${theme.gradient} text-white text-xs rounded-full font-semibold`}>
                            Popular
                          </div>
                        )}
                        
                        <div className="text-center">
                          <div className={`text-2xl mb-2 ${isSelected ? 'text-white' : 'text-gray-300'}`}>
                            {method.icon}
                          </div>
                          <h4 className={`font-semibold text-sm mb-1 ${isSelected ? 'text-white' : 'text-gray-200'}`}>
                            {method.name}
                          </h4>
                          <p className={`text-xs ${isSelected ? 'text-white/80' : 'text-gray-400'}`}>
                            Fee: {method.fee}
                          </p>
                        </div>
                        
                        {isSelected && (
                          <div className="absolute top-2 right-2">
                            <FaCheck className="text-white text-sm" />
                          </div>
                        )}
                      </motion.div>
                    );
                  })}
                </div>
              </div>

              {/* Payment Summary */}
              {selectedPaymentMethod && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.3 }}
                  className="max-w-2xl mx-auto"
                >
                  <div className={`bg-transparent border-2 ${theme.border} rounded-xl p-6`}>
                    <div className="flex items-center justify-center mb-4">
                      <FaLock className={`text-xl ${theme.text} mr-2`} />
                      <h3 className="text-xl font-bold text-white">Secure Checkout</h3>
                    </div>
                    
                    <div className="space-y-3 mb-6">
                      <div className="flex justify-between items-center">
                        <span className="text-gray-300">Course:</span>
                        <span className="text-white font-semibold">{courseData.name}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-300">Plan:</span>
                        <span className="text-white capitalize">{selectedTier}</span>
                      </div>
                      <div className="flex justify-between items-center">
                        <span className="text-gray-300">Payment Method:</span>
                        <span className="text-white">{paymentMethods.find(m => m.id === selectedPaymentMethod)?.name}</span>
                      </div>
                      <div className="border-t border-gray-600 pt-3">
                        <div className="flex justify-between items-center">
                          <span className="text-lg font-semibold text-gray-300">Total:</span>
                          <span className="text-2xl font-bold text-white">
                            ₹{pricingTiers[selectedTier].price.toLocaleString('en-IN')}
                          </span>
                        </div>
                      </div>
                    </div>
                    
                    <button className={`w-full py-3 px-6 bg-gradient-to-r ${theme.gradient} text-white font-semibold rounded-lg hover:opacity-90 transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl`}>
                      Complete Payment
                    </button>
                    
                    <div className="mt-4 flex items-center justify-center space-x-4 text-xs text-gray-400">
                      <div className="flex items-center space-x-1">
                        <FaLock className="text-green-400" />
                        <span>SSL Secured</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <FaCheck className="text-green-400" />
                        <span>256-bit Encryption</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <FaCheck className="text-green-400" />
                        <span>PCI Compliant</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              )}
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default PaymentModal;
