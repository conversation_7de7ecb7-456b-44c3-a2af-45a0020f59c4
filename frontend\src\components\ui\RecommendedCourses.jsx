import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { PaymentModal } from './';

// Custom SVG icons as components
const PythonIcon = () => (
  <svg viewBox="0 0 24 24" className="w-8 h-8 text-blue-400" fill="currentColor">
    <path d="M12 0C5.372 0 0 5.372 0 12s5.372 12 12 12 12-5.372 12-12S18.628 0 12 0zm5.696 5.858c.093.003.178.014.256.036.119.033.237.087.348.17.21.157.368.392.403.7.036.308-.052.637-.27.942-.085.12-.195.23-.327.334l-.043.032c-.698.52-1.833.591-2.618.162-.83-.453-1.282-1.443-1.095-2.325.168-.788.886-1.328 1.766-1.363.146-.007.291-.002.433.016.423.054.798.192 1.092.406.076.055.137.103.181.142.106.094.158.142.172.146.031.008.059-.016.078-.072.013-.038.019-.085.018-.141 0-.113-.026-.274-.083-.444-.096-.287-.26-.507-.52-.675a1.96 1.96 0 00-.7-.27 2.432 2.432 0 00-.51-.051c-.09 0-.174.004-.254.012l.072-.003zm-9.697.597c-.731.004-1.35.447-1.62 1.149-.286.746-.172 1.633.298 2.298.494.7 1.343 1.106 2.225 1.064.882-.042 1.68-.517 2.093-1.249.31-.551.355-1.188.13-1.759a1.892 1.892 0 00-.094-.194l-.04-.069c-.01-.016-.014-.024-.015-.026a.26.26 0 01-.007-.02c-.006-.016-.003-.03.009-.04.042-.036.171-.03.347.024.209.064.44.187.666.36.067.051.131.104.191.16.305.284.492.62.557 1.005.066.39-.003.8-.205 1.204-.205.41-.533.77-.96 1.054-.115.076-.24.147-.37.211-.244.12-.497.209-.752.262-.37.077-.739.091-1.095.04-.698-.101-1.332-.441-1.77-.98a2.564 2.564 0 01-.517-.972c-.063-.204-.098-.41-.104-.611a2.27 2.27 0 01.062-.676c.063-.239.16-.462.287-.668.267-.433.66-.762 1.144-.96.52-.213 1.108-.26 1.682-.142.144.03.284.07.42.122.216.081.419.189.603.321.272.196.502.435.68.71.075.115.139.236.193.36.103.237.174.484.212.737" />
  </svg>
);

const AIIcon = () => (
  <svg viewBox="0 0 24 24" className="w-8 h-8 text-purple-400" fill="currentColor">
    <path d="M12 2C7.58 2 4 5.58 4 10c0 2.03.76 3.87 2 5.28V20c0 1.1.9 2 2 2h8c1.1 0 2-.9 2-2v-4.72c1.24-1.41 2-3.25 2-5.28 0-4.42-3.58-8-8-8zm4 13.5c-.06.17-.15.33-.26.47l-.06.06c-.09.09-.19.16-.3.22-.12.06-.24.09-.38.09h-6c-.14 0-.26-.03-.38-.09-.11-.06-.21-.13-.3-.22l-.06-.06c-.11-.14-.2-.3-.26-.47-.06-.16-.09-.33-.09-.5V15c0-.28.11-.53.29-.71l.29-.29h6.86l.29.29c.18.18.29.43.29.71v.5c.01.17-.02.34-.08.5zm-4-4.5H8c-.55 0-1-.45-1-1s.45-1 1-1h8c.55 0 1 .45 1 1s-.45 1-1 1zm0-3H8c-.55 0-1-.45-1-1s.45-1 1-1h8c.55 0 1 .45 1 1s-.45 1-1 1z" />
  </svg>
);

const FullStackIcon = () => (
  <svg viewBox="0 0 24 24" className="w-8 h-8 text-cyan-400" fill="currentColor">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
  </svg>
);

const DataScienceIcon = () => (
  <svg viewBox="0 0 24 24" className="w-8 h-8 text-orange-400" fill="currentColor">
    <path d="M12 3C7.58 3 4 4.79 4 7s3.58 4 8 4 8-1.79 8-4-3.58-4-8-4zm0 6c-4.42 0-8-1.79-8-4v3c0 2.21 3.58 4 8 4s8-1.79 8-4V9c0 2.21-3.58 4-8 4zm0 5c-4.42 0-8-1.79-8-4v3c0 2.21 3.58 4 8 4s8-1.79 8-4v-3c0 2.21-3.58 4-8 4z" />
  </svg>
);

const DefaultIcon = () => (
  <svg viewBox="0 0 24 24" className="w-8 h-8 text-purple-400/50" fill="currentColor">
    <path d="M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z" />
  </svg>
);

// Additional course icons
const DSAIcon = () => (
  <svg viewBox="0 0 24 24" className="w-8 h-8 text-orange-400" fill="currentColor">
    <path d="M12 2L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z" />
  </svg>
);

const JavaScriptIcon = () => (
  <svg viewBox="0 0 24 24" className="w-8 h-8 text-yellow-400" fill="currentColor">
    <path d="M3 3h18v18H3V3zm16.525 13.707c-.131-.821-.666-1.511-2.252-2.155-.552-.259-1.165-.438-1.349-.854-.068-.248-.078-.382-.034-.529.113-.484.687-.629 1.137-.495.293.09.563.315.732.676.775-.507.775-.507 1.316-.844-.203-.314-.304-.451-.439-.586-.473-.528-1.103-.798-2.126-.77l-.528.067c-.507.124-.991.395-1.283.754-.855.968-.608 2.655.427 3.354 1.023.765 2.521.933 2.712 1.653.18.878-.652 1.159-1.475 1.058-.607-.136-.945-.439-1.316-1.002l-1.372.788c.157.359.337.517.607.832 1.305 1.316 4.568 1.249 5.153-.754.021-.067.18-.528.056-1.237l.034.049zm-6.737-5.434h-1.686c0 1.453-.007 2.898-.007 4.354 0 .924.047 1.772-.104 2.033-.247.517-.886.451-1.175.359-.297-.146-.448-.349-.623-.641-.047-.078-.082-.146-.095-.146l-1.368.844c.229.473.563.879.994 1.137.641.383 1.502.507 2.404.305.588-.17 1.095-.519 1.358-1.059.384-.697.302-1.553.299-2.509.008-1.541 0-3.083 0-4.635l.003-.042z" />
  </svg>
);

const DevOpsIcon = () => (
  <svg viewBox="0 0 24 24" className="w-8 h-8 text-green-400" fill="currentColor">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
  </svg>
);

const SystemDesignIcon = () => (
  <svg viewBox="0 0 24 24" className="w-8 h-8 text-teal-400" fill="currentColor">
    <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-1 17.93c-3.95-.49-7-3.85-7-7.93 0-.62.08-1.21.21-1.79L9 15v1c0 1.1.9 2 2 2v1.93zm6.9-2.54c-.26-.81-1-1.39-1.9-1.39h-1v-3c0-.55-.45-1-1-1H8v-2h2c.55 0 1-.45 1-1V7h2c1.1 0 2-.9 2-2v-.41c2.93 1.19 5 4.06 5 7.41 0 2.08-.8 3.97-2.1 5.39z" />
  </svg>
);

const CourseIcons = {
  python: PythonIcon,
  ai: AIIcon,
  fullstack: FullStackIcon,
  datascience: DataScienceIcon,
  dsa: DSAIcon,
  javascript: JavaScriptIcon,
  devops: DevOpsIcon,
  systemdesign: SystemDesignIcon,
  default: DefaultIcon
};

// Static fallback data for recommended courses
const staticRecommendedCourses = [
  {
    title: "Advanced System Design",
    type: "systemdesign",
    level: "Advanced",
    duration: "10 weeks",
    recommended: true
  },
  {
    title: "AI & Machine Learning",
    type: "ai",
    level: "Intermediate",
    duration: "16 weeks",
    recommended: true
  },
  {
    title: "DevOps & Cloud Computing",
    type: "devops",
    level: "Intermediate",
    duration: "12 weeks",
    recommended: true
  },
  {
    title: "Advanced Data Science",
    type: "datascience",
    level: "Advanced",
    duration: "18 weeks",
    recommended: true
  },
  {
    title: "Full Stack Architecture",
    type: "fullstack",
    level: "Advanced",
    duration: "20 weeks",
    recommended: true
  }
];

const RecommendedCourses = ({ advancedLabs }) => {
  const [isPaymentModalOpen, setIsPaymentModalOpen] = useState(false);
  const [selectedCourse, setSelectedCourse] = useState(null);

  const getIconComponent = (type) => {
    const IconComponent = CourseIcons[type?.toLowerCase()] || CourseIcons.default;
    return <IconComponent />;
  };

  // Use API data if available, otherwise use static data
  const coursesToDisplay = advancedLabs && advancedLabs.length > 0 ? advancedLabs : staticRecommendedCourses;

  const handleCourseClick = (course) => {
    const courseData = {
      name: course.title,
      labType: course.type,
      _id: course.type
    };
    setSelectedCourse(courseData);
    setIsPaymentModalOpen(true);
  };

  const closePaymentModal = () => {
    setIsPaymentModalOpen(false);
    setSelectedCourse(null);
  };

  return (
    <motion.div
      className="bg-gradient-to-br from-slate-900/10 via-blue-900/10 to-slate-900/10 backdrop-blur-sm rounded-2xl p-6 border border-white/10 shadow-[0_8px_30px_rgb(0,0,0,0.12)]"
      whileHover={{ scale: 1.01 }}
      transition={{ duration: 0.2 }}
    >
      <div className="flex items-center gap-3 mb-6">
        <div className="relative w-12 h-12 flex items-center justify-center">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl shadow-[0_0_15px_rgba(99,102,241,0.5)] animate-pulse" />
          <svg
            viewBox="0 0 24 24"
            className="w-7 h-7 text-white relative z-10"
            fill="currentColor"
          >
            <path d="M12 2L15.09 8.26L22 9.27L17 14.14L18.18 21.02L12 17.77L5.82 21.02L7 14.14L2 9.27L8.91 8.26L12 2" />
          </svg>
        </div>
        <h2 className="text-2xl font-bold text-white drop-shadow-[0_2px_4px_rgba(0,0,0,0.3)]">
          Recommended For You
        </h2>
      </div>
      <div className="space-y-4">
        {coursesToDisplay?.map((course, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: index * 0.1 }}
            className="group flex items-center gap-4 p-4 rounded-xl bg-gradient-to-r from-slate-800/30 to-purple-900/30 hover:from-slate-800/40 hover:to-purple-900/40 transition-all duration-300 cursor-pointer backdrop-blur-sm border border-white/10 hover:border-white/20"
            onClick={() => handleCourseClick(course)}
          >
            <div className="relative overflow-hidden rounded-lg flex items-center justify-center w-24 h-16 bg-slate-800/50">
              {getIconComponent(course.type)}
            </div>
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h3 className="font-medium text-white group-hover:text-purple-400 transition-colors duration-300">
                  {course.title}
                </h3>
                {course.recommended && (
                  <span className="bg-purple-500/20 px-2 py-0.5 rounded-full text-xs text-purple-300 border border-purple-500/30">
                    ⭐ Recommended
                  </span>
                )}
              </div>
              <div className="flex items-center gap-3 mt-2">
                <span className="bg-purple-500/20 px-3 py-1 rounded-full text-sm text-white border border-purple-500/30 shadow-[0_0_10px_rgba(168,85,247,0.2)] flex items-center gap-2">
                  <svg viewBox="0 0 24 24" className="w-4 h-4 text-purple-400" fill="currentColor">
                    <path d="M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z" />
                  </svg>
                  {course.level}
                </span>
                <span className="bg-purple-500/20 px-3 py-1 rounded-full text-sm text-white border border-purple-500/30 shadow-[0_0_10px_rgba(168,85,247,0.2)] flex items-center gap-2">
                  <svg viewBox="0 0 24 24" className="w-4 h-4 text-purple-400" fill="currentColor">
                    <path d="M12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22C6.47,22 2,17.5 2,12A10,10 0 0,1 12,2M12.5,7V12.25L17,14.92L16.25,16.15L11,13V7H12.5Z" />
                  </svg>
                  {course.duration}
                </span>
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={isPaymentModalOpen}
        onClose={closePaymentModal}
        courseData={selectedCourse}
      />
    </motion.div>
  );
};

export default RecommendedCourses;
