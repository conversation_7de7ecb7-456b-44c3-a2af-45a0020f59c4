import React from "react";
import { CourseResourcesSection } from "../../components/ui";
import CoursePaymentSection from "../../components/ui/CoursePaymentSection";
import { ReviewManager } from "../../components/reviews";
import useSidebarState from "../../hooks/useSidebarState";
import { DSAHero } from "./components";
import DSALayout from "./components/DSALayout";
import DSAPremiumModal from "./components/DSAPremiumModal";
import DSAIntroduction from "./components/DSAIntroduction";
import DSALabEnvironment from "./components/DSALabEnvironment";
import DSALiveClasses from "./components/DSALiveClasses";
import DSAFAQS from "./components/DSAFAQS";

const DSA_Course = () => {
  const courseConfig = {
    title: "Data Structures & Algorithms",
    subtitle: "Master DSA concepts through theory, practice exercises, and interview preparation",
    theme: {
      titleColor: "text-white",
      subtitleColor: "text-gray-300"
    },
    sections: [
      {
        id: "Introduction",
        title: "Introduction",
        description: "Learn DSA fundamentals and core concepts",
        icon: "📚",
        component: DSAIntroduction,
        props: {}
      },
      {
        id: "LabEnvironment",
        title: "Practice Lab",
        description: "Solve DSA problems in interactive environments",
        icon: "💻",
        component: DSALabEnvironment,
        props: {}
      },
      {
        id: "LiveClasses",
        title: "Interview Prep",
        description: "Prepare for technical interviews",
        icon: "🎯",
        component: DSALiveClasses,
        props: {}
      },
      {
        id: "FAQS",
        title: "FAQs",
        description: "Get answers to common DSA questions",
        icon: "❓",
        component: DSAFAQS,
        props: {}
      },
      {
        id: "Payment",
        title: "Enroll Now",
        description: "Choose your plan and payment method",
        icon: "💳",
        component: ({ showPremiumOverlay }) => (
          <CoursePaymentSection
            courseId="dsa-course"
            courseName="Data Structures & Algorithms Course"
            showPremiumOverlay={showPremiumOverlay}
          />
        ),
        props: {}
      },
      {
        id: "Reviews",
        title: "Reviews",
        description: "See what students are saying about this course",
        icon: "⭐",
        component: () => (
          <ReviewManager
            courseId="dsa-course"
            courseName="Data Structures & Algorithms Course"
            showWriteReview={true}
            showStats={true}
          />
        ),
        props: {}
      }
    ]
  };

  return (
    <CourseResourcesSection
      courseConfig={courseConfig}
      HeroComponent={DSAHero}
      LayoutComponent={DSALayout}
      PremiumModalComponent={DSAPremiumModal}
      useSidebarHook={useSidebarState}
    />
  );
};

export default DSA_Course;