import React from "react";
import { CourseResourcesSection } from "../../components/ui";
import CoursePaymentSection from "../../components/ui/CoursePaymentSection";
import { ReviewManager } from "../../components/reviews";
import useSidebarState from "../../hooks/useSidebarState";
import { SQLHero } from "./components";
import SQLLayout from "./components/SQLLayout";
import SQLPremiumModal from "./components/SQLPremiumModal";
import SQLIntroduction from "./components/SQLIntroduction";
import SQLLabEnvironment from "./components/SQLLabEnvironment";
import SQLLiveClasses from "./components/SQLLiveClasses";
import SQLFAQS from "./components/SQLFAQS";

const SQL_Course = () => {
  const courseConfig = {
    title: "SQL Mastery Course",
    subtitle: "Learn SQL from basics to advanced queries with practical exercises and interview preparation",
    theme: {
      titleColor: "text-white",
      subtitleColor: "text-gray-300"
    },
    sections: [
      {
        id: "Introduction",
        title: "Introduction",
        description: "Learn SQL fundamentals and core concepts",
        icon: "📚",
        component: SQLIntroduction,
        props: {}
      },
      {
        id: "LabEnvironment",
        title: "Practice Lab",
        description: "Solve SQL problems in interactive environments",
        icon: "💻",
        component: SQLLabEnvironment,
        props: {}
      },
      {
        id: "LiveClasses",
        title: "Interview Prep",
        description: "Prepare for SQL technical interviews",
        icon: "🎯",
        component: SQLLiveClasses,
        props: {}
      },
      {
        id: "FAQS",
        title: "FAQs",
        description: "Get answers to common SQL questions",
        icon: "❓",
        component: SQLFAQS,
        props: {}
      },
      {
        id: "Payment",
        title: "Enroll Now",
        description: "Choose your plan and payment method",
        icon: "💳",
        component: ({ showPremiumOverlay }) => (
          <CoursePaymentSection
            courseId="sql-course"
            courseName="SQL Database Course"
            showPremiumOverlay={showPremiumOverlay}
          />
        ),
        props: {}
      },
      {
        id: "Reviews",
        title: "Reviews",
        description: "See what students are saying about this course",
        icon: "⭐",
        component: () => (
          <ReviewManager
            courseId="sql-course"
            courseName="SQL Course"
            showWriteReview={true}
            showStats={true}
          />
        ),
        props: {}
      }
    ]
  };

  return (
    <CourseResourcesSection
      courseConfig={courseConfig}
      HeroComponent={SQLHero}
      LayoutComponent={SQLLayout}
      PremiumModalComponent={SQLPremiumModal}
      useSidebarHook={useSidebarState}
    />
  );
};

export default SQL_Course;