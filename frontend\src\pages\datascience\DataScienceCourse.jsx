import InternshipApply from "../../components/InternshipApply";
import React from "react";
import Introduction from "./Introduction";
import FAQS from "./FAQS";
import DataScienceLayout from "./components/DataScienceLayout";
import DataScienceHero from "./components/DataScienceHero";
import DataScienceLabEnvironmentNew from "./components/DataScienceLabEnvironmentNew";
import DataScienceLiveClasses from "./components/DataScienceLiveClasses";
import DataSciencePremiumModal from "./components/DataSciencePremiumModal";
import { CourseResourcesSection } from "../../components/ui";
import CoursePaymentSection from "../../components/ui/CoursePaymentSection";
import { ReviewManager } from "../../components/reviews";
import useSidebarState from "../../hooks/useSidebarState";

const DataScienceCourse = () => {
  const courseConfig = {
    title: "Data Science Course Resources",
    subtitle: "Master data science through comprehensive theory, hands-on projects, live workshops, and expert guidance",
    theme: {
      titleColor: "text-white",
      subtitleColor: "text-white/80"
    },
    sections: [
      {
        id: "Introduction",
        title: "Introduction",
        description: "Learn data science fundamentals and methodologies",
        icon: "📊",
        component: Introduction,
        props: {}
      },
      {
        id: "LabEnvironment",
        title: "Lab Environment",
        description: "Practice with real-world datasets and tools",
        icon: "🔬",
        component: DataScienceLabEnvironmentNew,
        props: {}
      },
      {
        id: "LiveClasses",
        title: "Live Classes",
        description: "Join expert-led data science workshops",
        icon: "🎓",
        component: DataScienceLiveClasses,
        props: {}
      },
      {
        id: "FAQS",
        title: "FAQs",
        description: "Get answers to data science questions",
        icon: "❓",
        component: FAQS,
        props: {}
      },
      {
        id: "Payment",
        title: "Enroll Now",
        description: "Choose your plan and payment method",
        icon: "💳",
        component: ({ showPremiumOverlay }) => (
          <CoursePaymentSection
            courseId="datascience-course"
            courseName="Data Science Course"
            showPremiumOverlay={showPremiumOverlay}
          />
        ),
        props: {}
      },
      {
        id: "Reviews",
        title: "Reviews",
        description: "See what students are saying about this course",
        icon: "⭐",
        component: () => (
          <ReviewManager
            courseId="datascience-course"
            courseName="Data Science Course"
            showWriteReview={true}
            showStats={true}
          />
        ),
        props: {}
      }
    ]
  };

  return (
    <>
      <CourseResourcesSection
        courseConfig={courseConfig}
        HeroComponent={DataScienceHero}
        LayoutComponent={DataScienceLayout}
        PremiumModalComponent={DataSciencePremiumModal}
        useSidebarHook={useSidebarState}
      />
      {/* Removed duplicate JSX */}
      <InternshipApply />
    </>
  );
};

export default DataScienceCourse;
