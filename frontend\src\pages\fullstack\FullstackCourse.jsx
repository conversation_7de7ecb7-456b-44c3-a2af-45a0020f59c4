import React from "react";
import FullstackLayout from "./components/FullstackLayout";
import FullstackHero from "./components/FullstackHero";
import FullstackIntroduction from "./Introduction";
import FullstackLabEnvironment from "./components/FullstackLabEnvironment";
import FullstackLiveClasses from "./components/FullstackLiveClasses";
import FullstackFAQS from "./FAQS";
import FullstackPremiumModal from "./components/FullstackPremiumModal";
import { CourseResourcesSection } from "../../components/ui";
import CoursePaymentSection from "../../components/ui/CoursePaymentSection";
import { ReviewManager } from "../../components/reviews";
import useSidebarState from "../../hooks/useSidebarState";

const FullstackCourse = () => {
  const courseConfig = {
    title: "Full Stack Learning Paths",
    subtitle: "Master both frontend and backend development through comprehensive learning modules and hands-on projects",
    theme: {
      titleColor: "text-white",
      subtitleColor: "text-gray-300"
    },
    sections: [
      {
        id: "Introduction",
        title: "Introduction",
        description: "Learn full-stack development foundations",
        icon: "🌐",
        component: FullstackIntroduction,
        props: {}
      },
      {
        id: "LabEnvironment",
        title: "Lab Environment",
        description: "Practice with development environments",
        icon: "⚙️",
        component: FullstackLabEnvironment,
        props: {}
      },
      {
        id: "LiveClasses",
        title: "Live Classes",
        description: "Join live coding sessions and workshops",
        icon: "🔴",
        component: FullstackLiveClasses,
        props: {}
      },
      {
        id: "FAQS",
        title: "FAQs",
        description: "Get answers to development questions",
        icon: "💡",
        component: FullstackFAQS,
        props: {}
      },
      {
        id: "Payment",
        title: "Enroll Now",
        description: "Choose your plan and payment method",
        icon: "💳",
        component: ({ showPremiumOverlay }) => (
          <CoursePaymentSection
            courseId="fullstack-course"
            courseName="Full Stack Development Course"
            showPremiumOverlay={showPremiumOverlay}
          />
        ),
        props: {}
      },
      {
        id: "Reviews",
        title: "Reviews",
        description: "See what students are saying about this course",
        icon: "⭐",
        component: () => (
          <ReviewManager
            courseId="fullstack-course"
            courseName="Full Stack Development Course"
            showWriteReview={true}
            showStats={true}
          />
        ),
        props: {}
      }
    ]
  };

  return (
    <CourseResourcesSection
      courseConfig={courseConfig}
      HeroComponent={FullstackHero}
      LayoutComponent={FullstackLayout}
      PremiumModalComponent={FullstackPremiumModal}
      useSidebarHook={useSidebarState}
    />
  );
};

export default FullstackCourse;
