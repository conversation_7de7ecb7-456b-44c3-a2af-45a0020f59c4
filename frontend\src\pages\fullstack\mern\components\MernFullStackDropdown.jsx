import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { FaChevronDown, FaChevronUp } from "react-icons/fa";

const MernFullStackDropdown = ({ topics }) => {
  const [openSections, setOpenSections] = useState({});

  const toggleSection = (sectionName) => {
    setOpenSections(prev => ({
      ...prev,
      [sectionName]: !prev[sectionName]
    }));
  };

  return (
    <div className="space-y-4">
      <h3 className="text-xl font-bold text-white mb-4">Course Curriculum</h3>
      
      {Object.entries(topics).map(([sectionName, sectionTopics], sectionIndex) => (
        <motion.div
          key={sectionName}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: sectionIndex * 0.1 }}
          className="bg-gray-900/50 border border-gray-700/30 rounded-lg overflow-hidden"
        >
          <button
            onClick={() => toggleSection(sectionName)}
            className="w-full px-6 py-4 flex items-center justify-between text-left hover:bg-gray-800/50 transition-colors"
          >
            <h4 className="text-lg font-semibold text-white">{sectionName}</h4>
            {openSections[sectionName] ? (
              <FaChevronUp className="text-gray-400" />
            ) : (
              <FaChevronDown className="text-gray-400" />
            )}
          </button>
          
          <AnimatePresence>
            {openSections[sectionName] && (
              <motion.div
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={{ duration: 0.3 }}
                className="border-t border-gray-700/30"
              >
                <div className="p-6 space-y-4">
                  {sectionTopics.map((topic, topicIndex) => (
                    <motion.div
                      key={topicIndex}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: topicIndex * 0.1 }}
                      className="bg-gray-800/50 border border-gray-700/30 rounded-lg p-4"
                    >
                      <h5 className="text-lg font-medium text-white mb-2">{topic.title}</h5>
                      
                      {/* Tags */}
                      <div className="flex flex-wrap gap-2 mb-3">
                        {topic.tags.map((tag, tagIndex) => (
                          <span
                            key={tagIndex}
                            className="px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full border border-green-500/30"
                          >
                            {tag}
                          </span>
                        ))}
                      </div>
                      
                      {/* Key Points */}
                      <ul className="space-y-1">
                        {topic.keyPoints.map((point, pointIndex) => (
                          <li key={pointIndex} className="text-gray-300 text-sm flex items-start">
                            <span className="w-2 h-2 bg-blue-400 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                            {point}
                          </li>
                        ))}
                      </ul>
                    </motion.div>
                  ))}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </motion.div>
      ))}
    </div>
  );
};

export default MernFullStackDropdown;
