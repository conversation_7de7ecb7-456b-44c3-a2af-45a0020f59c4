import React from "react";
import { motion } from "framer-motion";

const Mern<PERSON>ullStackHero = () => {
  return (
    <div className="relative min-h-screen bg-gradient-to-br from-[#1a3c50] to-[#010509] text-white overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <svg className="w-full h-full" xmlns="http://www.w3.org/2000/svg">
          <defs>
            <pattern
              id="mern-pattern"
              patternUnits="userSpaceOnUse"
              width="60"
              height="60"
              patternTransform="rotate(45)"
            >
              <circle cx="30" cy="30" r="2" fill="rgba(34,197,94,0.3)" />
              <circle cx="15" cy="15" r="1" fill="rgba(59,130,246,0.2)" />
              <circle cx="45" cy="45" r="1" fill="rgba(251,191,36,0.2)" />
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#mern-pattern)" />
        </svg>
      </div>

      <div className="relative z-10 flex items-center justify-center min-h-screen px-6">
        <div className="max-w-6xl mx-auto text-center">
          
        {/* Animated code pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-0 left-0 right-0 bottom-0 text-xs md:text-sm overflow-hidden text-gray-400 font-mono">
            {Array.from({ length: 20 }).map((_, i) => (
              <motion.div
                key={i}
                initial={{ opacity: 0.3, x: -10 }}
                animate={{ opacity: [0.1, 0.3, 0.1], x: 0 }}
                transition={{ duration: 10, repeat: Infinity, delay: i * 0.2 }}
                className="whitespace-nowrap"
                style={{ position: 'absolute', top: `${i * 5}%` }}
              >
                {`const express = require('express');\nconst mongoose = require('mongoose');\nconst app = express();\n\napp.get('/api/users', async (req, res) => {\n  const users = await User.find();\n  res.json(users);\n});`.repeat(2)}
              </motion.div>
            ))}
          </div>
        </div>

          {/* Main Content */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="relative z-20"
          >
            {/* MERN Stack Icons */}
            <div className="flex justify-center items-center space-x-8 mb-8">
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.2, duration: 0.5 }}
                className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center border border-green-500/30"
              >
                <span className="text-2xl font-bold text-green-400">M</span>
              </motion.div>
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.4, duration: 0.5 }}
                className="w-16 h-16 bg-gray-500/20 rounded-full flex items-center justify-center border border-gray-500/30"
              >
                <span className="text-2xl font-bold text-gray-300">E</span>
              </motion.div>
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.6, duration: 0.5 }}
                className="w-16 h-16 bg-blue-500/20 rounded-full flex items-center justify-center border border-blue-500/30"
              >
                <span className="text-2xl font-bold text-blue-400">R</span>
              </motion.div>
              <motion.div
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                transition={{ delay: 0.8, duration: 0.5 }}
                className="w-16 h-16 bg-green-600/20 rounded-full flex items-center justify-center border border-green-600/30"
              >
                <span className="text-2xl font-bold text-green-500">N</span>
              </motion.div>
            </div>

            <h1 className="text-5xl md:text-7xl font-bold mb-6 bg-gradient-to-r from-green-400 via-blue-400 to-yellow-400 bg-clip-text text-transparent">
              MERN Stack
            </h1>
            
            <h2 className="text-2xl md:text-3xl font-semibold mb-6 text-gray-300">
              Full Stack Development
            </h2>
            
            <p className="text-lg md:text-xl text-gray-400 mb-8 max-w-3xl mx-auto leading-relaxed">
              Master the complete MERN stack - MongoDB, Express.js, React, and Node.js. 
              Build modern, scalable web applications from frontend to backend.
            </p>

            {/* Tech Stack Highlights */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12 max-w-4xl mx-auto">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1, duration: 0.5 }}
                className="bg-green-500/10 border border-green-500/20 rounded-lg p-4 backdrop-blur-sm"
              >
                <h3 className="font-semibold text-green-400 mb-2">MongoDB</h3>
                <p className="text-sm text-gray-400">NoSQL Database</p>
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.2, duration: 0.5 }}
                className="bg-gray-500/10 border border-gray-500/20 rounded-lg p-4 backdrop-blur-sm"
              >
                <h3 className="font-semibold text-gray-300 mb-2">Express.js</h3>
                <p className="text-sm text-gray-400">Backend Framework</p>
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.4, duration: 0.5 }}
                className="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4 backdrop-blur-sm"
              >
                <h3 className="font-semibold text-blue-400 mb-2">React</h3>
                <p className="text-sm text-gray-400">Frontend Library</p>
              </motion.div>
              
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.6, duration: 0.5 }}
                className="bg-green-600/10 border border-green-600/20 rounded-lg p-4 backdrop-blur-sm"
              >
                <h3 className="font-semibold text-green-500 mb-2">Node.js</h3>
                <p className="text-sm text-gray-400">Runtime Environment</p>
              </motion.div>
            </div>

            {/* CTA Buttons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.8, duration: 0.5 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <button className="px-8 py-4 bg-gradient-to-r from-green-500 to-blue-500 text-white font-semibold rounded-lg hover:from-green-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105 shadow-lg">
                Start Learning MERN
              </button>
              <button className="px-8 py-4 border border-gray-500 text-gray-300 font-semibold rounded-lg hover:border-gray-400 hover:text-white transition-all duration-300 backdrop-blur-sm">
                View Curriculum
              </button>
            </motion.div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default MernFullStackHero;
