import React, { useState } from "react";
import { motion } from "framer-motion";
import Editor from "@monaco-editor/react";

const MernFullStackLabEnvironment = ({ onBackToCourse, showPremiumOverlay }) => {
  const [selectedChallenge, setSelectedChallenge] = useState("");
  const [htmlCode, setHtmlCode] = useState(`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MERN Challenge</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="root">
        <h1>Welcome to MERN Stack</h1>
        <p>Start building your application here!</p>
    </div>
    <script src="script.js"></script>
</body>
</html>`);

  const [cssCode, setCssCode] = useState(`/* MERN Stack Styles */
body {
    font-family: 'Arial', sans-serif;
    margin: 0;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
}

#root {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 20px;
}

.mern-container {
    display: flex;
    justify-content: space-around;
    margin: 20px 0;
}

.tech-card {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    text-align: center;
    transition: transform 0.3s ease;
}

.tech-card:hover {
    transform: translateY(-5px);
}`);

  const [jsCode, setJsCode] = useState(`// MERN Stack JavaScript
console.log('Welcome to MERN Stack Development!');

// MongoDB Connection Example
const mongoose = require('mongoose');

// Express Server Setup
const express = require('express');
const app = express();

// React Component Example
function MernApp() {
    const [data, setData] = React.useState([]);
    
    React.useEffect(() => {
        fetchData();
    }, []);
    
    const fetchData = async () => {
        try {
            const response = await fetch('/api/data');
            const result = await response.json();
            setData(result);
        } catch (error) {
            console.error('Error fetching data:', error);
        }
    };
    
    return (
        <div className="mern-app">
            <h2>MERN Stack Application</h2>
            <div className="data-container">
                {data.map(item => (
                    <div key={item.id} className="data-item">
                        {item.name}
                    </div>
                ))}
            </div>
        </div>
    );
}

// Node.js API Endpoint
app.get('/api/data', (req, res) => {
    res.json([
        { id: 1, name: 'MongoDB' },
        { id: 2, name: 'Express.js' },
        { id: 3, name: 'React' },
        { id: 4, name: 'Node.js' }
    ]);
});

app.listen(3000, () => {
    console.log('MERN server running on port 3000');
});`);

  const challenges = [
    { value: "", label: "Select a Challenge" },
    { value: "todo-app", label: "Todo Application with MERN" },
    { value: "user-auth", label: "User Authentication System" },
    { value: "blog-platform", label: "Blog Platform with CRUD" },
    { value: "ecommerce-cart", label: "E-commerce Shopping Cart" },
    { value: "chat-app", label: "Real-time Chat Application" },
    { value: "social-media", label: "Social Media Dashboard" }
  ];

  const handleChallengeChange = (e) => {
    const challenge = e.target.value;
    setSelectedChallenge(challenge);
    
    // Update code based on selected challenge
    if (challenge === "todo-app") {
      setHtmlCode(`<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MERN Todo App</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="root">
        <div class="todo-container">
            <h1>MERN Todo Application</h1>
            <div class="todo-input">
                <input type="text" id="todoInput" placeholder="Add a new task...">
                <button onclick="addTodo()">Add Task</button>
            </div>
            <ul id="todoList" class="todo-list"></ul>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>`);
      
      setCssCode(`/* Todo App Styles */
.todo-container {
    max-width: 600px;
    margin: 50px auto;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.todo-input {
    display: flex;
    margin-bottom: 20px;
}

.todo-input input {
    flex: 1;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px 0 0 5px;
    font-size: 16px;
}

.todo-input button {
    padding: 12px 20px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 0 5px 5px 0;
    cursor: pointer;
}

.todo-list {
    list-style: none;
    padding: 0;
}

.todo-item {
    padding: 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}`);
    }
  };

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-900/70 to-blue-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">MERN Stack Lab Environment</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Lab Challenges Instructions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-6 bg-blue-500/10 border border-blue-500/20 rounded-lg p-4"
        >
          <h3 className="text-lg font-semibold text-blue-400 mb-3">Lab Challenges</h3>
          <ol className="text-gray-300 space-y-2">
            <li>1. Select a challenge from the dropdown</li>
            <li>2. Write HTML, CSS, and JavaScript in the Monaco editors</li>
            <li>3. See live preview updates as you type</li>
            <li>4. Submit your solution for evaluation</li>
          </ol>
        </motion.div>

        {/* Challenge Selector */}
        <div className="mb-6">
          <label className="block text-white font-medium mb-2">Choose Challenge:</label>
          <select
            value={selectedChallenge}
            onChange={handleChallengeChange}
            className="w-full p-3 bg-gray-900 border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-green-400 focus:border-green-400"
          >
            {challenges.map((challenge) => (
              <option key={challenge.value} value={challenge.value}>
                {challenge.label}
              </option>
            ))}
          </select>
        </div>

        {/* Code Editors */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-6">
          {/* HTML Editor */}
          <div className="bg-gray-900/50 border border-gray-700/30 rounded-lg overflow-hidden">
            <div className="bg-orange-600/20 px-4 py-2 border-b border-gray-700/30">
              <h4 className="font-medium text-orange-400">HTML</h4>
            </div>
            <div className="h-64">
              <Editor
                height="100%"
                defaultLanguage="html"
                value={htmlCode}
                onChange={(value) => setHtmlCode(value || "")}
                theme="vs-dark"
                options={{
                  minimap: { enabled: false },
                  fontSize: 14,
                  wordWrap: "on",
                  automaticLayout: true
                }}
              />
            </div>
          </div>

          {/* CSS Editor */}
          <div className="bg-gray-900/50 border border-gray-700/30 rounded-lg overflow-hidden">
            <div className="bg-blue-600/20 px-4 py-2 border-b border-gray-700/30">
              <h4 className="font-medium text-blue-400">CSS</h4>
            </div>
            <div className="h-64">
              <Editor
                height="100%"
                defaultLanguage="css"
                value={cssCode}
                onChange={(value) => setCssCode(value || "")}
                theme="vs-dark"
                options={{
                  minimap: { enabled: false },
                  fontSize: 14,
                  wordWrap: "on",
                  automaticLayout: true
                }}
              />
            </div>
          </div>

          {/* JavaScript Editor */}
          <div className="bg-gray-900/50 border border-gray-700/30 rounded-lg overflow-hidden">
            <div className="bg-yellow-600/20 px-4 py-2 border-b border-gray-700/30">
              <h4 className="font-medium text-yellow-400">JavaScript</h4>
            </div>
            <div className="h-64">
              <Editor
                height="100%"
                defaultLanguage="javascript"
                value={jsCode}
                onChange={(value) => setJsCode(value || "")}
                theme="vs-dark"
                options={{
                  minimap: { enabled: false },
                  fontSize: 14,
                  wordWrap: "on",
                  automaticLayout: true
                }}
              />
            </div>
          </div>
        </div>

        {/* Live Preview */}
        <div className="mb-6">
          <div className="bg-gray-900/50 border border-gray-700/30 rounded-lg overflow-hidden">
            <div className="bg-green-600/20 px-4 py-2 border-b border-gray-700/30">
              <h4 className="font-medium text-green-400">Live Preview</h4>
            </div>
            <div className="h-64 bg-white">
              <iframe
                srcDoc={`
                  ${htmlCode}
                  <style>${cssCode}</style>
                  <script>${jsCode}</script>
                `}
                className="w-full h-full border-none"
                title="Live Preview"
              />
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex gap-4">
          <button 
            onClick={showPremiumOverlay}
            className="px-6 py-3 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-lg font-medium hover:from-green-600 hover:to-blue-600 transition-all duration-300 transform hover:scale-105"
          >
            Submit Solution
          </button>
          <button 
            onClick={() => {
              setHtmlCode("");
              setCssCode("");
              setJsCode("");
            }}
            className="px-6 py-3 bg-gray-600 text-white rounded-lg font-medium hover:bg-gray-700 transition-colors"
          >
            Reset Code
          </button>
        </div>
      </div>
    </div>
  );
};

export default MernFullStackLabEnvironment;
