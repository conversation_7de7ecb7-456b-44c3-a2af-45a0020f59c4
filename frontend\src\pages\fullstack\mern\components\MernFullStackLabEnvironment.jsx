import React, { useState, useRef, useCallback, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import Editor from "@monaco-editor/react";
import {
  FaPlus,
  FaTimes,
  FaFolder,
  FaFile,
  FaSave,
  FaPlay,
  FaSun,
  FaMoon,
  FaExpand,
  FaCompress,
  FaCog,
  FaTerminal,
  FaEye
} from "react-icons/fa";

const MernFullStackLabEnvironment = ({ onBackToCourse, showPremiumOverlay }) => {
  // Theme state
  const [theme, setTheme] = useState('vs-dark');
  const [selectedChallenge, setSelectedChallenge] = useState("");

  // File system state
  const [files, setFiles] = useState({
    'index.html': {
      id: 'index.html',
      name: 'index.html',
      language: 'html',
      content: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MERN Challenge</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="root">
        <h1>Welcome to MERN Stack</h1>
        <p>Start building your application here!</p>
        <div class="mern-container">
            <div class="tech-card">MongoDB</div>
            <div class="tech-card">Express.js</div>
            <div class="tech-card">React</div>
            <div class="tech-card">Node.js</div>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>`,
      saved: true
    },
    'styles.css': {
      id: 'styles.css',
      name: 'styles.css',
      language: 'css',
      content: `/* MERN Stack Styles */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 0;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #333;
    min-height: 100vh;
}

#root {
    max-width: 1000px;
    margin: 0 auto;
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    backdrop-filter: blur(10px);
}

h1 {
    color: #2c3e50;
    text-align: center;
    margin-bottom: 30px;
    font-size: 2.5rem;
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
}

.mern-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.tech-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 30px 20px;
    border-radius: 12px;
    text-align: center;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    font-weight: 600;
    font-size: 1.1rem;
    color: #495057;
}

.tech-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 30px rgba(0,0,0,0.15);
    border-color: #667eea;
}

.tech-card:nth-child(1) { border-left: 4px solid #4CAF50; }
.tech-card:nth-child(2) { border-left: 4px solid #FF9800; }
.tech-card:nth-child(3) { border-left: 4px solid #2196F3; }
.tech-card:nth-child(4) { border-left: 4px solid #8BC34A; }`,
      saved: true
    },
    'script.js': {
      id: 'script.js',
      name: 'script.js',
      language: 'javascript',
      content: `// MERN Stack JavaScript
console.log('🚀 Welcome to MERN Stack Development!');

// Simulate React-like functionality
class MernApp {
    constructor() {
        this.data = [
            { id: 1, name: 'MongoDB', description: 'NoSQL Database' },
            { id: 2, name: 'Express.js', description: 'Backend Framework' },
            { id: 3, name: 'React', description: 'Frontend Library' },
            { id: 4, name: 'Node.js', description: 'Runtime Environment' }
        ];
        this.init();
    }

    init() {
        this.addInteractivity();
        this.simulateApiCall();
    }

    addInteractivity() {
        const cards = document.querySelectorAll('.tech-card');
        cards.forEach((card, index) => {
            card.addEventListener('click', () => {
                this.showTechInfo(this.data[index]);
            });

            card.style.cursor = 'pointer';
        });
    }

    showTechInfo(tech) {
        alert(\`\${tech.name}: \${tech.description}\`);
        console.log('Tech selected:', tech);
    }

    async simulateApiCall() {
        console.log('📡 Simulating API call...');

        // Simulate async operation
        setTimeout(() => {
            console.log('✅ API Response:', this.data);
            this.updateUI();
        }, 1000);
    }

    updateUI() {
        const root = document.getElementById('root');
        const statusDiv = document.createElement('div');
        statusDiv.innerHTML = \`
            <p style="text-align: center; color: #28a745; font-weight: bold; margin-top: 20px;">
                ✅ MERN Stack Components Loaded Successfully!
            </p>
        \`;
        root.appendChild(statusDiv);
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MernApp();
});

// Express.js API simulation (for learning purposes)
const simulateExpressAPI = {
    routes: {
        '/api/users': { method: 'GET', data: [{ id: 1, name: 'John Doe' }] },
        '/api/posts': { method: 'GET', data: [{ id: 1, title: 'Hello MERN' }] }
    },

    get(route) {
        console.log(\`📍 API GET: \${route}\`);
        return this.routes[route]?.data || { error: 'Route not found' };
    }
};

// MongoDB simulation
const simulateMongoDB = {
    collections: {
        users: [{ _id: '1', name: 'John', email: '<EMAIL>' }],
        posts: [{ _id: '1', title: 'First Post', content: 'Hello World!' }]
    },

    find(collection) {
        console.log(\`🗄️  MongoDB Query: \${collection}\`);
        return this.collections[collection] || [];
    }
};

console.log('🔧 MERN Stack Environment Ready!');`,
      saved: true
    }
  });

  // Active file and tab management
  const [activeFileId, setActiveFileId] = useState('index.html');
  const [openTabs, setOpenTabs] = useState(['index.html', 'styles.css', 'script.js']);

  // Layout state
  const [layout, setLayout] = useState({
    showSidebar: false, // Changed to false for full-width editor
    showConsole: true,
    showPreview: false, // Changed to false since it's now a modal
    sidebarWidth: 250,
    consoleHeight: 200
  });

  // Preview modal state
  const [isPreviewModalOpen, setIsPreviewModalOpen] = useState(false);

  // Console state
  const [consoleOutput, setConsoleOutput] = useState([
    { type: 'info', message: 'Welcome to MERN Stack Lab Environment', timestamp: new Date() },
    { type: 'success', message: 'Monaco Editor initialized successfully', timestamp: new Date() }
  ]);

  // Editor refs
  const editorRef = useRef(null);
  const [isEditorReady, setIsEditorReady] = useState(false);

  // Console functions (defined first to avoid dependency issues)
  const addConsoleMessage = useCallback((type, message) => {
    setConsoleOutput(prev => [...prev, {
      type,
      message,
      timestamp: new Date()
    }]);
  }, []);

  // Preview modal functions
  const openPreviewModal = useCallback(() => {
    setIsPreviewModalOpen(true);
    addConsoleMessage('info', 'Opening live preview...');
  }, [addConsoleMessage]);

  const closePreviewModal = useCallback(() => {
    setIsPreviewModalOpen(false);
    addConsoleMessage('info', 'Closed live preview');
  }, [addConsoleMessage]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event) => {
      if (event.key === 'Escape' && isPreviewModalOpen) {
        closePreviewModal();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isPreviewModalOpen, closePreviewModal]);

  const challenges = [
    { value: "", label: "Select a Challenge" },
    { value: "todo-app", label: "Todo Application with MERN" },
    { value: "user-auth", label: "User Authentication System" },
    { value: "blog-platform", label: "Blog Platform with CRUD" },
    { value: "ecommerce-cart", label: "E-commerce Shopping Cart" },
    { value: "chat-app", label: "Real-time Chat Application" },
    { value: "social-media", label: "Social Media Dashboard" }
  ];

  // Utility functions
  const getFileIcon = (filename) => {
    const ext = filename.split('.').pop().toLowerCase();
    switch (ext) {
      case 'html': return '🌐';
      case 'css': return '🎨';
      case 'js': return '⚡';
      case 'json': return '📋';
      case 'md': return '📝';
      default: return '📄';
    }
  };

  const getLanguageFromFilename = (filename) => {
    const ext = filename.split('.').pop().toLowerCase();
    switch (ext) {
      case 'html': return 'html';
      case 'css': return 'css';
      case 'js': return 'javascript';
      case 'json': return 'json';
      case 'md': return 'markdown';
      case 'tsx': return 'typescript';
      case 'ts': return 'typescript';
      default: return 'plaintext';
    }
  };

  // File management functions
  const createNewFile = useCallback(() => {
    const fileName = prompt('Enter file name (with extension):');
    if (fileName && !files[fileName]) {
      const newFile = {
        id: fileName,
        name: fileName,
        language: getLanguageFromFilename(fileName),
        content: '',
        saved: false
      };
      setFiles(prev => ({ ...prev, [fileName]: newFile }));
      setOpenTabs(prev => [...prev, fileName]);
      setActiveFileId(fileName);
      addConsoleMessage('info', `Created new file: ${fileName}`);
    }
  }, [files]);

  const deleteFile = useCallback((fileId) => {
    if (Object.keys(files).length <= 1) {
      addConsoleMessage('error', 'Cannot delete the last file');
      return;
    }

    const newFiles = { ...files };
    delete newFiles[fileId];
    setFiles(newFiles);

    const newTabs = openTabs.filter(tab => tab !== fileId);
    setOpenTabs(newTabs);

    if (activeFileId === fileId) {
      setActiveFileId(newTabs[0] || Object.keys(newFiles)[0]);
    }

    addConsoleMessage('info', `Deleted file: ${fileId}`);
  }, [files, openTabs, activeFileId]);

  const renameFile = useCallback((oldName, newName) => {
    if (files[newName]) {
      addConsoleMessage('error', 'File with this name already exists');
      return;
    }

    const file = files[oldName];
    const newFile = {
      ...file,
      id: newName,
      name: newName,
      language: getLanguageFromFilename(newName),
      saved: false
    };

    const newFiles = { ...files };
    delete newFiles[oldName];
    newFiles[newName] = newFile;
    setFiles(newFiles);

    setOpenTabs(prev => prev.map(tab => tab === oldName ? newName : tab));
    if (activeFileId === oldName) {
      setActiveFileId(newName);
    }

    addConsoleMessage('info', `Renamed ${oldName} to ${newName}`);
  }, [files, activeFileId]);

  const saveFile = useCallback((fileId) => {
    setFiles(prev => ({
      ...prev,
      [fileId]: { ...prev[fileId], saved: true }
    }));
    addConsoleMessage('success', `Saved: ${fileId}`);
  }, []);

  const saveAllFiles = useCallback(() => {
    setFiles(prev => {
      const newFiles = { ...prev };
      Object.keys(newFiles).forEach(key => {
        newFiles[key] = { ...newFiles[key], saved: true };
      });
      return newFiles;
    });
    addConsoleMessage('success', 'All files saved');
  }, []);

  const clearConsole = useCallback(() => {
    setConsoleOutput([]);
  }, []);

  // Theme functions
  const toggleTheme = useCallback(() => {
    setTheme(prev => prev === 'vs-dark' ? 'light' : 'vs-dark');
    addConsoleMessage('info', `Switched to ${theme === 'vs-dark' ? 'light' : 'dark'} theme`);
  }, [theme]);

  // Editor functions
  const handleEditorDidMount = useCallback((editor, monaco) => {
    editorRef.current = editor;
    setIsEditorReady(true);

    // Configure Monaco Editor
    monaco.editor.defineTheme('mern-dark', {
      base: 'vs-dark',
      inherit: true,
      rules: [
        { token: 'comment', foreground: '6A9955' },
        { token: 'keyword', foreground: '569CD6' },
        { token: 'string', foreground: 'CE9178' },
        { token: 'number', foreground: 'B5CEA8' }
      ],
      colors: {
        'editor.background': '#1e1e1e',
        'editor.foreground': '#d4d4d4'
      }
    });

    addConsoleMessage('success', 'Monaco Editor ready');
  }, []);

  const handleEditorChange = useCallback((value, event) => {
    if (activeFileId && value !== undefined) {
      setFiles(prev => ({
        ...prev,
        [activeFileId]: {
          ...prev[activeFileId],
          content: value,
          saved: false
        }
      }));
    }
  }, [activeFileId]);

  // Layout functions
  const togglePanel = useCallback((panel) => {
    setLayout(prev => ({
      ...prev,
      [panel]: !prev[panel]
    }));
  }, []);



  // Run code function
  const runCode = useCallback(() => {
    addConsoleMessage('info', 'Running code...');
    try {
      // Simulate code execution
      setTimeout(() => {
        addConsoleMessage('success', 'Code executed successfully');
      }, 1000);
    } catch (error) {
      addConsoleMessage('error', `Error: ${error.message}`);
    }
  }, []);

  // Challenge handler
  const handleChallengeChange = useCallback((e) => {
    const challenge = e.target.value;
    setSelectedChallenge(challenge);

    if (challenge === "todo-app") {
      setFiles({
        'index.html': {
          id: 'index.html',
          name: 'index.html',
          language: 'html',
          content: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MERN Todo App</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="root">
        <div class="todo-container">
            <h1>MERN Todo Application</h1>
            <div class="todo-input">
                <input type="text" id="todoInput" placeholder="Add a new task...">
                <button onclick="addTodo()">Add Task</button>
            </div>
            <ul id="todoList" class="todo-list"></ul>
        </div>
    </div>
    <script src="script.js"></script>
</body>
</html>`,
          saved: false
        },
        'styles.css': {
          id: 'styles.css',
          name: 'styles.css',
          language: 'css',
          content: `/* Todo App Styles */
.todo-container {
    max-width: 600px;
    margin: 50px auto;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.todo-input {
    display: flex;
    margin-bottom: 20px;
}

.todo-input input {
    flex: 1;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px 0 0 5px;
    font-size: 16px;
}

.todo-input button {
    padding: 12px 20px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 0 5px 5px 0;
    cursor: pointer;
}

.todo-list {
    list-style: none;
    padding: 0;
}

.todo-item {
    padding: 15px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}`,
          saved: false
        },
        'script.js': {
          id: 'script.js',
          name: 'script.js',
          language: 'javascript',
          content: `// MERN Todo App JavaScript
let todos = [];
let todoId = 1;

function addTodo() {
    const input = document.getElementById('todoInput');
    const text = input.value.trim();

    if (text) {
        const todo = {
            id: todoId++,
            text: text,
            completed: false
        };

        todos.push(todo);
        input.value = '';
        renderTodos();
    }
}

function deleteTodo(id) {
    todos = todos.filter(todo => todo.id !== id);
    renderTodos();
}

function toggleTodo(id) {
    const todo = todos.find(todo => todo.id === id);
    if (todo) {
        todo.completed = !todo.completed;
        renderTodos();
    }
}

function renderTodos() {
    const todoList = document.getElementById('todoList');
    todoList.innerHTML = '';

    todos.forEach(todo => {
        const li = document.createElement('li');
        li.className = 'todo-item';
        li.innerHTML = \`
            <span style="text-decoration: \${todo.completed ? 'line-through' : 'none'}">
                \${todo.text}
            </span>
            <div>
                <button onclick="toggleTodo(\${todo.id})" style="margin-right: 10px;">
                    \${todo.completed ? 'Undo' : 'Complete'}
                </button>
                <button onclick="deleteTodo(\${todo.id})" style="background: #f44336;">
                    Delete
                </button>
            </div>
        \`;
        todoList.appendChild(li);
    });
}

// Allow Enter key to add todo
document.addEventListener('DOMContentLoaded', () => {
    const input = document.getElementById('todoInput');
    input.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            addTodo();
        }
    });
});`,
          saved: false
        }
      });
      addConsoleMessage('info', 'Loaded Todo App challenge');
    }
  }, []);

  // Get current file
  const currentFile = files[activeFileId];

  // Generate live preview content
  const generatePreviewContent = useCallback(() => {
    const htmlFile = files['index.html'];
    const cssFile = files['styles.css'];
    const jsFile = files['script.js'];

    if (!htmlFile) return '<p>No HTML file found</p>';

    return `
      ${htmlFile.content}
      <style>${cssFile?.content || ''}</style>
      <script>${jsFile?.content || ''}</script>
    `;
  }, [files]);

  return (
    <div className="bg-gray-900 text-white min-h-screen flex flex-col">
      {/* Top Menu Bar */}
      <div className="bg-gray-800 border-b border-gray-700 px-4 py-2 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <button
            onClick={onBackToCourse}
            className="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-sm transition-colors"
          >
            ← Back to Course
          </button>
          <h2 className="text-lg font-semibold">MERN Stack Lab Environment</h2>

          {/* Challenge Selector in Top Bar */}
          <div className="flex items-center space-x-2">
            <label className="text-sm text-gray-300">Challenge:</label>
            <select
              value={selectedChallenge}
              onChange={handleChallengeChange}
              className="px-3 py-1 bg-gray-700 border border-gray-600 rounded text-sm text-white focus:ring-1 focus:ring-green-400 focus:border-green-400"
            >
              {challenges.map((challenge) => (
                <option key={challenge.value} value={challenge.value}>
                  {challenge.label}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={toggleTheme}
            className="p-2 hover:bg-gray-700 rounded transition-colors"
            title="Toggle Theme"
          >
            {theme === 'vs-dark' ? <FaSun className="text-yellow-400" /> : <FaMoon className="text-blue-400" />}
          </button>
          <button
            onClick={saveAllFiles}
            className="p-2 hover:bg-gray-700 rounded transition-colors"
            title="Save All Files"
          >
            <FaSave className="text-green-400" />
          </button>
          <button
            onClick={runCode}
            className="p-2 hover:bg-gray-700 rounded transition-colors"
            title="Run Code"
          >
            <FaPlay className="text-blue-400" />
          </button>
          <button
            onClick={openPreviewModal}
            className="p-2 hover:bg-gray-700 rounded transition-colors"
            title="Open Live Preview"
          >
            <FaEye className="text-green-400" />
          </button>
          <button
            onClick={() => togglePanel('showSidebar')}
            className="p-2 hover:bg-gray-700 rounded transition-colors"
            title="Toggle Sidebar"
          >
            <FaFolder className="text-gray-400" />
          </button>
        </div>
      </div>

      {/* Lab Instructions Banner */}
      <div className="bg-blue-900/20 border-b border-blue-700/30 px-6 py-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <h3 className="text-sm font-semibold text-blue-400">Lab Challenges Instructions</h3>
            <div className="flex items-center space-x-6 text-xs text-gray-300">
              <span>1. Select a challenge from the dropdown</span>
              <span>2. Write HTML, CSS, and JavaScript in the Monaco editor</span>
              <span>3. See live preview updates as you type</span>
              <span>4. Submit your solution for evaluation</span>
            </div>
          </div>
          <button
            onClick={() => togglePanel('showSidebar')}
            className="px-3 py-1 bg-gray-700 hover:bg-gray-600 rounded text-xs transition-colors"
            title="Toggle File Explorer"
          >
            <FaFolder className="inline mr-1" />
            Files
          </button>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex flex-1 overflow-hidden">
        {/* Sidebar */}
        {layout.showSidebar && (
          <div className="w-64 bg-gray-800 border-r border-gray-700 flex flex-col">
            {/* Lab Challenges Instructions */}
            <div className="p-4 border-b border-gray-700">
              <h3 className="text-sm font-semibold text-blue-400 mb-3">Lab Challenges</h3>
              <ol className="text-xs text-gray-300 space-y-1">
                <li>1. Select a challenge from the dropdown</li>
                <li>2. Write HTML, CSS, and JavaScript in the Monaco editors</li>
                <li>3. See live preview updates as you type</li>
                <li>4. Submit your solution for evaluation</li>
              </ol>
            </div>

            {/* Challenge Selector */}
            <div className="p-4 border-b border-gray-700">
              <label className="block text-xs font-medium text-gray-400 mb-2">Choose Challenge:</label>
              <select
                value={selectedChallenge}
                onChange={handleChallengeChange}
                className="w-full p-2 bg-gray-900 border border-gray-600 rounded text-sm text-white focus:ring-1 focus:ring-green-400 focus:border-green-400"
              >
                {challenges.map((challenge) => (
                  <option key={challenge.value} value={challenge.value}>
                    {challenge.label}
                  </option>
                ))}
              </select>
            </div>

            {/* File Explorer */}
            <div className="flex-1 p-4">
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-semibold text-gray-300">Files</h3>
                <button
                  onClick={createNewFile}
                  className="p-1 hover:bg-gray-700 rounded transition-colors"
                  title="New File"
                >
                  <FaPlus className="text-xs text-gray-400" />
                </button>
              </div>

              <div className="space-y-1">
                {Object.values(files).map((file) => (
                  <div
                    key={file.id}
                    className={`flex items-center justify-between p-2 rounded cursor-pointer transition-colors ${
                      activeFileId === file.id
                        ? 'bg-blue-600/30 text-blue-300'
                        : 'hover:bg-gray-700 text-gray-300'
                    }`}
                    onClick={() => setActiveFileId(file.id)}
                  >
                    <div className="flex items-center space-x-2">
                      <span className="text-xs">{getFileIcon(file.name)}</span>
                      <span className="text-sm">{file.name}</span>
                      {!file.saved && <span className="w-2 h-2 bg-orange-400 rounded-full"></span>}
                    </div>

                    <div className="flex items-center space-x-1">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          const newName = prompt('Enter new name:', file.name);
                          if (newName && newName !== file.name) {
                            renameFile(file.name, newName);
                          }
                        }}
                        className="p-1 hover:bg-gray-600 rounded transition-colors opacity-0 group-hover:opacity-100"
                        title="Rename"
                      >
                        <FaFile className="text-xs text-gray-400" />
                      </button>

                      {Object.keys(files).length > 1 && (
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            if (confirm(`Delete ${file.name}?`)) {
                              deleteFile(file.id);
                            }
                          }}
                          className="p-1 hover:bg-gray-600 rounded transition-colors opacity-0 group-hover:opacity-100"
                          title="Delete"
                        >
                          <FaTimes className="text-xs text-red-400" />
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Main Editor Area */}
        <div className="flex-1 flex flex-col">
          {/* Tab Bar */}
          <div className="bg-gray-800 border-b border-gray-700 flex items-center">
            {openTabs.map((tabId) => {
              const file = files[tabId];
              if (!file) return null;

              return (
                <div
                  key={tabId}
                  className={`flex items-center px-4 py-2 border-r border-gray-700 cursor-pointer transition-colors ${
                    activeFileId === tabId
                      ? 'bg-gray-900 text-white'
                      : 'bg-gray-800 text-gray-400 hover:text-white hover:bg-gray-750'
                  }`}
                  onClick={() => setActiveFileId(tabId)}
                >
                  <span className="text-xs mr-2">{getFileIcon(file.name)}</span>
                  <span className="text-sm">{file.name}</span>
                  {!file.saved && <span className="w-1.5 h-1.5 bg-orange-400 rounded-full ml-2"></span>}

                  {openTabs.length > 1 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        const newTabs = openTabs.filter(tab => tab !== tabId);
                        setOpenTabs(newTabs);
                        if (activeFileId === tabId && newTabs.length > 0) {
                          setActiveFileId(newTabs[0]);
                        }
                      }}
                      className="ml-2 p-1 hover:bg-gray-600 rounded transition-colors"
                    >
                      <FaTimes className="text-xs" />
                    </button>
                  )}
                </div>
              );
            })}
          </div>

          {/* Editor Container */}
          <div className="flex-1 flex">
            {/* Code Editor - Now takes full width */}
            <div className="flex-1 bg-gray-900">
              {currentFile && (
                <Editor
                  height="100%"
                  language={currentFile.language}
                  value={currentFile.content}
                  onChange={handleEditorChange}
                  onMount={handleEditorDidMount}
                  theme={theme === 'vs-dark' ? 'mern-dark' : 'light'}
                  options={{
                    minimap: { enabled: false },
                    fontSize: 14,
                    wordWrap: "on",
                    automaticLayout: true,
                    scrollBeyondLastLine: false,
                    renderWhitespace: 'selection',
                    selectOnLineNumbers: true,
                    roundedSelection: false,
                    readOnly: false,
                    cursorStyle: 'line',
                    glyphMargin: true,
                    folding: true,
                    lineNumbers: 'on',
                    lineDecorationsWidth: 10,
                    lineNumbersMinChars: 3,
                    renderLineHighlight: 'all',
                    contextmenu: true,
                    mouseWheelZoom: true,
                    smoothScrolling: true,
                    cursorBlinking: 'blink',
                    cursorSmoothCaretAnimation: true,
                    renderFinalNewline: true,
                    quickSuggestions: true,
                    suggestOnTriggerCharacters: true,
                    acceptSuggestionOnEnter: 'on',
                    tabCompletion: 'on',
                    wordBasedSuggestions: true,
                    parameterHints: { enabled: true },
                    autoClosingBrackets: 'always',
                    autoClosingQuotes: 'always',
                    autoSurround: 'languageDefined',
                    colorDecorators: true,
                    dragAndDrop: true,
                    find: {
                      seedSearchStringFromSelection: true,
                      autoFindInSelection: 'never'
                    }
                  }}
                />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Console Panel */}
      {layout.showConsole && (
        <div className="bg-gray-900 border-t border-gray-700" style={{ height: `${layout.consoleHeight}px` }}>
          <div className="bg-gray-800 px-4 py-2 border-b border-gray-700 flex items-center justify-between">
            <h4 className="text-sm font-medium text-yellow-400 flex items-center">
              <FaTerminal className="mr-2" />
              Console
            </h4>
            <div className="flex items-center space-x-2">
              <button
                onClick={clearConsole}
                className="px-2 py-1 text-xs bg-gray-700 hover:bg-gray-600 rounded transition-colors"
              >
                Clear
              </button>
              <button
                onClick={() => togglePanel('showConsole')}
                className="p-1 hover:bg-gray-700 rounded transition-colors"
                title="Close Console"
              >
                <FaTimes className="text-xs text-gray-400" />
              </button>
            </div>
          </div>

          <div className="p-4 h-full overflow-y-auto font-mono text-sm">
            {consoleOutput.map((log, index) => (
              <div key={index} className="mb-1 flex items-start space-x-2">
                <span className="text-gray-500 text-xs">
                  {log.timestamp.toLocaleTimeString()}
                </span>
                <span className={`${
                  log.type === 'error' ? 'text-red-400' :
                  log.type === 'success' ? 'text-green-400' :
                  log.type === 'warning' ? 'text-yellow-400' :
                  'text-gray-300'
                }`}>
                  {log.message}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Bottom Action Bar */}
      <div className="bg-gray-800 border-t border-gray-700 px-4 py-2 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <span className="text-xs text-gray-400">
            {currentFile ? `${currentFile.name} • ${currentFile.language}` : 'No file selected'}
          </span>
          {currentFile && !currentFile.saved && (
            <span className="text-xs text-orange-400">• Unsaved changes</span>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={() => togglePanel('showConsole')}
            className={`px-3 py-1 text-xs rounded transition-colors ${
              layout.showConsole
                ? 'bg-yellow-600 text-white'
                : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
            }`}
          >
            <FaTerminal className="inline mr-1" />
            Console
          </button>

          <button
            onClick={openPreviewModal}
            className="px-3 py-1 text-xs rounded transition-colors bg-gray-700 text-gray-300 hover:bg-gray-600 hover:text-white"
            title="Quick Preview (also available in top toolbar)"
          >
            <FaEye className="inline mr-1" />
            Quick Preview
          </button>

          <button
            onClick={showPremiumOverlay}
            className="px-4 py-1 bg-gradient-to-r from-green-500 to-blue-500 text-white text-xs rounded font-medium hover:from-green-600 hover:to-blue-600 transition-all duration-300"
          >
            Submit Solution
          </button>
        </div>
      </div>

      {/* Live Preview Modal */}
      <AnimatePresence>
        {isPreviewModalOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3 }}
              className="bg-white rounded-lg shadow-2xl w-full max-w-6xl h-full max-h-[90vh] flex flex-col"
            >
            {/* Modal Header */}
            <div className="bg-gray-800 text-white px-6 py-4 rounded-t-lg flex items-center justify-between border-b">
              <h3 className="text-lg font-semibold flex items-center">
                <FaEye className="mr-2 text-green-400" />
                Live Preview - MERN Stack Application
              </h3>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => {
                    const previewContent = generatePreviewContent();
                    const newWindow = window.open('', '_blank');
                    newWindow.document.write(previewContent);
                    newWindow.document.close();
                  }}
                  className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded transition-colors"
                  title="Open in New Window"
                >
                  <FaExpand className="inline mr-1" />
                  Pop Out
                </button>
                <button
                  onClick={closePreviewModal}
                  className="p-2 hover:bg-gray-700 rounded transition-colors"
                  title="Close Preview"
                >
                  <FaTimes className="text-gray-400 hover:text-white" />
                </button>
              </div>
            </div>

            {/* Modal Content */}
            <div className="flex-1 bg-gray-100 relative overflow-hidden">
              {/* Preview Controls */}
              <div className="absolute top-0 left-0 right-0 bg-gray-200 px-4 py-2 border-b flex items-center justify-between z-10">
                <div className="flex items-center space-x-4">
                  <span className="text-sm text-gray-600">
                    🌐 localhost:3000
                  </span>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => {
                        // Refresh preview
                        const iframe = document.getElementById('preview-iframe');
                        if (iframe) {
                          iframe.src = iframe.src;
                        }
                      }}
                      className="px-2 py-1 bg-gray-300 hover:bg-gray-400 text-gray-700 text-xs rounded transition-colors"
                    >
                      🔄 Refresh
                    </button>
                    <span className="text-xs text-gray-500">
                      Auto-refresh: ON
                    </span>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-500">
                    {currentFile ? `Editing: ${currentFile.name}` : 'No file selected'}
                  </span>
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" title="Live Preview Active"></div>
                </div>
              </div>

              {/* Preview Iframe */}
              <div className="pt-12 h-full">
                <iframe
                  id="preview-iframe"
                  srcDoc={generatePreviewContent()}
                  className="w-full h-full border-none bg-white"
                  title="Live Preview"
                  sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
                />
              </div>
            </div>

            {/* Modal Footer */}
            <div className="bg-gray-800 text-white px-6 py-3 rounded-b-lg flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <span className="text-sm text-gray-300">
                  ✅ Live preview is running
                </span>
                <span className="text-xs text-gray-400">
                  Changes will appear automatically
                </span>
              </div>

              <div className="flex items-center space-x-2">
                <button
                  onClick={() => {
                    addConsoleMessage('info', 'Preview refreshed manually');
                    const iframe = document.getElementById('preview-iframe');
                    if (iframe) {
                      iframe.contentWindow.location.reload();
                    }
                  }}
                  className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white text-sm rounded transition-colors"
                >
                  🔄 Refresh Preview
                </button>
                <button
                  onClick={closePreviewModal}
                  className="px-4 py-1 bg-gray-600 hover:bg-gray-700 text-white text-sm rounded transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </motion.div>
        </div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default MernFullStackLabEnvironment;
