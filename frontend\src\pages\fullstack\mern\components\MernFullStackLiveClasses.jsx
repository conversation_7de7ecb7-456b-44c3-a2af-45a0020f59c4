import React from "react";
import { motion } from "framer-motion";
import { FaCalendarAlt, FaClock, FaUsers, FaVideo } from "react-icons/fa";

const MernFullStackLiveClasses = ({ onBackToCourse, showPremiumOverlay }) => {
  const liveClasses = [
    {
      title: "MERN Stack Project Setup",
      date: "2024-01-15",
      time: "10:00 AM - 12:00 PM",
      instructor: "<PERSON>",
      participants: 45,
      status: "upcoming",
      topics: ["Project Structure", "Environment Setup", "Git Workflow"]
    },
    {
      title: "React Components & State Management",
      date: "2024-01-17",
      time: "2:00 PM - 4:00 PM",
      instructor: "<PERSON>",
      participants: 38,
      status: "upcoming",
      topics: ["Functional Components", "Hooks", "Context API"]
    },
    {
      title: "Node.js & Express API Development",
      date: "2024-01-20",
      time: "11:00 AM - 1:00 PM",
      instructor: "<PERSON>",
      participants: 42,
      status: "upcoming",
      topics: ["REST APIs", "Middleware", "Error Handling"]
    },
    {
      title: "MongoDB Integration & Mongoose",
      date: "2024-01-22",
      time: "3:00 PM - 5:00 PM",
      instructor: "<PERSON>",
      participants: 35,
      status: "upcoming",
      topics: ["Database Design", "Schemas", "Relationships"]
    }
  ];

  const upcomingProjects = [
    {
      title: "Social Media Dashboard",
      description: "Build a complete social media platform with user authentication, posts, and real-time updates",
      duration: "4 weeks",
      difficulty: "Intermediate",
      technologies: ["React", "Node.js", "MongoDB", "Socket.io"]
    },
    {
      title: "E-commerce Platform",
      description: "Create a full-featured online store with payment integration and admin panel",
      duration: "6 weeks",
      difficulty: "Advanced",
      technologies: ["React", "Express", "MongoDB", "Stripe API"]
    },
    {
      title: "Task Management System",
      description: "Develop a collaborative task management tool with team features",
      duration: "3 weeks",
      difficulty: "Beginner",
      technologies: ["React", "Node.js", "MongoDB", "JWT"]
    }
  ];

  return (
    <div className="bg-gray-800/40 backdrop-blur-sm border border-gray-700/30 rounded-xl shadow-lg overflow-hidden">
      {/* Header */}
      <div className="bg-gradient-to-r from-green-900/70 to-blue-900/70 px-6 py-4 flex justify-between items-center border-b border-gray-700/30">
        <h2 className="text-xl font-bold text-white">MERN Stack Live Classes & Projects</h2>
        <button 
          onClick={onBackToCourse}
          className="px-4 py-2 bg-white/20 hover:bg-white/30 text-white rounded-lg transition-colors"
        >
          Back to Course
        </button>
      </div>

      {/* Content */}
      <div className="p-6">
        {/* Live Classes Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          className="mb-8"
        >
          <h3 className="text-2xl font-bold text-white mb-6">Upcoming Live Classes</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {liveClasses.map((classItem, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-gray-900/50 border border-gray-700/30 rounded-lg p-6 hover:border-green-500/30 transition-colors"
              >
                <div className="flex items-start justify-between mb-4">
                  <h4 className="text-lg font-semibold text-white">{classItem.title}</h4>
                  <span className="px-2 py-1 bg-green-500/20 text-green-300 text-xs rounded-full border border-green-500/30">
                    {classItem.status}
                  </span>
                </div>
                
                <div className="space-y-3 mb-4">
                  <div className="flex items-center text-gray-300">
                    <FaCalendarAlt className="mr-2 text-blue-400" />
                    <span className="text-sm">{classItem.date}</span>
                  </div>
                  <div className="flex items-center text-gray-300">
                    <FaClock className="mr-2 text-yellow-400" />
                    <span className="text-sm">{classItem.time}</span>
                  </div>
                  <div className="flex items-center text-gray-300">
                    <FaUsers className="mr-2 text-green-400" />
                    <span className="text-sm">{classItem.participants} participants</span>
                  </div>
                </div>

                <div className="mb-4">
                  <p className="text-sm text-gray-400 mb-2">Instructor: {classItem.instructor}</p>
                  <div className="flex flex-wrap gap-2">
                    {classItem.topics.map((topic, topicIndex) => (
                      <span
                        key={topicIndex}
                        className="px-2 py-1 bg-blue-500/20 text-blue-300 text-xs rounded border border-blue-500/30"
                      >
                        {topic}
                      </span>
                    ))}
                  </div>
                </div>

                <button 
                  onClick={showPremiumOverlay}
                  className="w-full px-4 py-2 bg-gradient-to-r from-green-500 to-blue-500 text-white rounded-lg font-medium hover:from-green-600 hover:to-blue-600 transition-all duration-300 flex items-center justify-center"
                >
                  <FaVideo className="mr-2" />
                  Join Live Class
                </button>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Projects Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
        >
          <h3 className="text-2xl font-bold text-white mb-6">Hands-on Projects</h3>
          <div className="space-y-6">
            {upcomingProjects.map((project, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="bg-gray-900/50 border border-gray-700/30 rounded-lg p-6"
              >
                <div className="flex flex-col md:flex-row md:items-start md:justify-between mb-4">
                  <div className="flex-1">
                    <h4 className="text-xl font-semibold text-white mb-2">{project.title}</h4>
                    <p className="text-gray-300 mb-4">{project.description}</p>
                  </div>
                  <div className="flex flex-col md:items-end space-y-2">
                    <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                      project.difficulty === 'Beginner' ? 'bg-green-500/20 text-green-300 border border-green-500/30' :
                      project.difficulty === 'Intermediate' ? 'bg-yellow-500/20 text-yellow-300 border border-yellow-500/30' :
                      'bg-red-500/20 text-red-300 border border-red-500/30'
                    }`}>
                      {project.difficulty}
                    </span>
                    <span className="text-sm text-gray-400">{project.duration}</span>
                  </div>
                </div>

                <div className="flex flex-wrap gap-2 mb-4">
                  {project.technologies.map((tech, techIndex) => (
                    <span
                      key={techIndex}
                      className="px-3 py-1 bg-purple-500/20 text-purple-300 text-sm rounded border border-purple-500/30"
                    >
                      {tech}
                    </span>
                  ))}
                </div>

                <button 
                  onClick={showPremiumOverlay}
                  className="px-6 py-2 bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg font-medium hover:from-purple-600 hover:to-pink-600 transition-all duration-300"
                >
                  Start Project
                </button>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default MernFullStackLiveClasses;
