{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@monaco-editor/react": "^4.6.0", "@react-oauth/google": "^0.12.1", "@reduxjs/toolkit": "^2.6.0", "axios": "^1.7.9", "bootstrap": "^5.3.3", "chart.js": "^4.4.7", "framer-motion": "^12.6.2", "monaco-editor": "^0.52.2", "re-resizable": "^6.11.2", "react": "^19.0.0", "react-calendar": "^5.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "^19.0.0", "react-hot-toast": "^2.5.2", "react-icons": "^5.4.0", "react-redux": "^9.2.0", "react-router-dom": "^7.1.5"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "postcss": "^8.4.49", "sass-embedded": "^1.86.0", "tailwindcss": "^3.4.17", "vite": "^6.1.0"}}